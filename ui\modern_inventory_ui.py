"""
واجهة إدارة المخزون العصرية 2025
Modern Inventory Management UI 2025
"""
import tkinter as tk
from tkinter import ttk, messagebox

class ModernInventoryFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8fafc')
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6', 
            'accent': '#06b6d4',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'dark': '#1f2937',
            'gray': '#6b7280',
            'light_gray': '#f3f4f6',
            'white': '#ffffff'
        }
        self.create_modern_widgets()

    def create_modern_widgets(self):
        """إنشاء عناصر الواجهة العصرية"""
        # الشريط العلوي
        top_frame = tk.Frame(self, bg='white', padx=30, pady=25)
        top_frame.pack(fill='x', padx=30, pady=(30, 0))
        
        # العنوان الرئيسي
        title_label = tk.Label(top_frame, text="📦 إدارة المخزون", 
                              font=('Segoe UI', 24, 'bold'),
                              bg='white', fg=self.colors['dark'])
        title_label.pack(side='left')
        
        # زر إضافة قطعة جديدة
        add_btn = tk.Button(top_frame, text="➕ قطعة جديدة",
                           font=('Segoe UI', 12, 'bold'),
                           bg=self.colors['success'], fg='white',
                           relief='flat', padx=25, pady=12,
                           command=self.add_item_dialog)
        add_btn.pack(side='right')
        
        # الوصف
        desc_label = tk.Label(top_frame, text="إدارة شاملة لقطع الغيار والمخزون",
                             font=('Segoe UI', 11),
                             bg='white', fg=self.colors['gray'])
        desc_label.pack(anchor='w', pady=(10, 0))
        
        # بطاقات الإحصائيات
        stats_frame = tk.Frame(self, bg='#f8fafc')
        stats_frame.pack(fill='x', padx=30, pady=20)
        
        # بطاقات المخزون
        stats_data = [
            ("📦", "إجمالي القطع", "156", self.colors['primary']),
            ("✅", "متوفر", "120", self.colors['success']),
            ("⚠️", "مخزون منخفض", "25", self.colors['warning']),
            ("❌", "نفد المخزون", "11", self.colors['danger'])
        ]
        
        for icon, title, count, color in stats_data:
            card = tk.Frame(stats_frame, bg='white', relief='flat', bd=0, padx=20, pady=15)
            card.pack(side='left', fill='x', expand=True, padx=5)
            
            tk.Label(card, text=icon, font=('Segoe UI Emoji', 24),
                    bg='white', fg=color).pack()
            
            tk.Label(card, text=count, font=('Segoe UI', 20, 'bold'),
                    bg='white', fg=self.colors['dark']).pack()
            
            tk.Label(card, text=title, font=('Segoe UI', 11),
                    bg='white', fg=self.colors['gray']).pack()
        
        # منطقة الجدول
        table_frame = tk.Frame(self, bg='white', padx=30, pady=20)
        table_frame.pack(fill='both', expand=True, padx=30, pady=(15, 30))
        
        # عنوان الجدول
        tk.Label(table_frame, text="📋 قائمة المخزون", 
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w', pady=(0, 15))
        
        # جدول المخزون
        columns = ("id", "name", "part_number", "quantity", "price", "status")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        headers = {
            "id": "الرقم",
            "name": "اسم القطعة",
            "part_number": "رقم القطعة",
            "quantity": "الكمية",
            "price": "السعر",
            "status": "الحالة"
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            self.tree.column(col, width=150)
        
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # أزرار العمليات
        buttons_frame = tk.Frame(table_frame, bg='white')
        buttons_frame.pack(fill='x', pady=(15, 0))
        
        buttons = [
            ("✏️ تعديل", self.edit_item, self.colors['warning']),
            ("🔄 تحديث الكمية", self.update_quantity, self.colors['primary']),
            ("⚠️ تنبيهات المخزون", self.show_alerts, self.colors['danger']),
            ("📊 تقرير المخزون", self.generate_report, self.colors['secondary'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(buttons_frame, text=text, command=command,
                           font=('Segoe UI', 10, 'bold'),
                           bg=color, fg='white', relief='flat',
                           padx=15, pady=8)
            btn.pack(side='left', padx=5)

    def add_item_dialog(self):
        """نافذة إضافة قطعة جديدة"""
        messagebox.showinfo("قطعة جديدة", "نافذة إضافة قطعة جديدة قيد التطوير")

    def edit_item(self):
        """تعديل القطعة"""
        messagebox.showinfo("تعديل", "نافذة تعديل القطعة قيد التطوير")

    def update_quantity(self):
        """تحديث الكمية"""
        messagebox.showinfo("تحديث الكمية", "نافذة تحديث الكمية قيد التطوير")

    def show_alerts(self):
        """عرض تنبيهات المخزون"""
        messagebox.showinfo("تنبيهات", "نافذة تنبيهات المخزون قيد التطوير")

    def generate_report(self):
        """إنشاء تقرير المخزون"""
        messagebox.showinfo("تقرير", "تقرير المخزون قيد التطوير")
