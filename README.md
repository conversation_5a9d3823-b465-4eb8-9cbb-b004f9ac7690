# نظام إدارة محل صيانة الجوالات
## Phone Repair Shop Management System

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![Tkinter](https://img.shields.io/badge/GUI-Tkinter-green.svg)
![SQLite](https://img.shields.io/badge/Database-SQLite-orange.svg)
![License](https://img.shields.io/badge/License-Open%20Source-brightgreen.svg)

## 📱 وصف المشروع

نظام شامل ومتكامل لإدارة محل صيانة الجوالات باللغة العربية، مصمم خصيصاً لأصحاب محلات صيانة الهواتف المحمولة والأجهزة الإلكترونية. يوفر النظام حلولاً متقدمة لإدارة جميع جوانب العمل من العملاء والطلبات إلى المخزون والتقارير المالية.

## ✨ الميزات الرئيسية

### 👥 إدارة العملاء
- ✅ إضافة عملاء جدد مع جميع البيانات الضرورية
- ✅ تعديل وتحديث بيانات العملاء الموجودين
- ✅ البحث السريع في قاعدة بيانات العملاء
- ✅ عرض سجل كامل لطلبات كل عميل
- ✅ طباعة بيانات العميل وتاريخه
- ✅ التحقق من صحة البيانات المدخلة

### 🔧 إدارة طلبات الصيانة
- ✅ إنشاء طلبات صيانة جديدة بتفاصيل شاملة
- ✅ تتبع حالة الطلبات (في الانتظار، قيد الإصلاح، مكتمل، تم التسليم)
- ✅ تحديث حالة الطلبات مع إضافة ملاحظات
- ✅ ربط الطلبات بالعملاء والفنيين
- ✅ حساب التكلفة المقدرة والفعلية
- ✅ طباعة تفاصيل الطلب وإيصالات التسليم

### 📦 إدارة المخزون
- ✅ إضافة قطع غيار جديدة مع تفاصيل كاملة
- ✅ تحديث كميات المخزون (إضافة، خصم، تعديل)
- ✅ تنبيهات تلقائية للمخزون المنخفض
- ✅ تقارير شاملة لحالة المخزون
- ✅ حساب القيمة الإجمالية للمخزون
- ✅ تتبع أرقام القطع والموردين

### 💰 إدارة المدفوعات
- ✅ تسجيل المدفوعات الجزئية والكاملة
- ✅ طرق دفع متعددة (نقداً، بطاقة، تحويل)
- ✅ ربط المدفوعات بطلبات الصيانة
- ✅ تتبع المبالغ المستحقة والمدفوعة
- ✅ إيصالات دفع قابلة للطباعة

### 📊 التقارير والإحصائيات
- ✅ لوحة معلومات شاملة مع إحصائيات فورية
- ✅ تقارير الإيرادات اليومية والشهرية
- ✅ تقارير المصروفات والتكاليف
- ✅ تحليل أداء الفنيين ومعدلات الإنجاز
- ✅ قائمة أفضل العملاء وأكثرهم تعاملاً
- ✅ تقارير المخزون والقطع الناقصة

## 🛠️ التقنيات المستخدمة

| التقنية | الغرض | الإصدار |
|---------|--------|---------|
| **Python** | لغة البرمجة الأساسية | 3.7+ |
| **Tkinter** | واجهة المستخدم الرسومية | مدمجة |
| **SQLite** | قاعدة البيانات المحلية | مدمجة |
| **Matplotlib** | الرسوم البيانية والتقارير | 3.5+ |
| **ReportLab** | توليد ملفات PDF | 3.6+ |
| **Pillow** | معالجة الصور | 9.0+ |

## 🚀 كيفية التشغيل

### التشغيل السريع
```bash
# الطريقة الأسهل - تشغيل مباشر
python run.py
```

### التشغيل التقليدي
```bash
# تشغيل التطبيق الرئيسي
python main.py
```

### تثبيت المتطلبات (اختياري)
```bash
# تثبيت المكتبات الإضافية للميزات المتقدمة
pip install -r requirements.txt
```

## 📋 متطلبات النظام

### المتطلبات الأساسية
- **نظام التشغيل:** Windows 10+, macOS 10.14+, أو Linux
- **Python:** الإصدار 3.7 أو أحدث
- **الذاكرة:** 512 MB RAM كحد أدنى
- **التخزين:** 100 MB مساحة فارغة

### المكتبات المطلوبة
**مدمجة مع Python (لا تحتاج تثبيت):**
- `tkinter` - واجهة المستخدم
- `sqlite3` - قاعدة البيانات
- `datetime` - التاريخ والوقت
- `re` - التعبيرات النمطية

**إضافية (للميزات المتقدمة):**
- `matplotlib` - الرسوم البيانية
- `reportlab` - ملفات PDF
- `pillow` - معالجة الصور
- `openpyxl` - ملفات Excel

## 📁 بنية المشروع

```
اختبار تطبيق حسابات python/
├── 📄 main.py                    # نقطة التشغيل الرئيسية
├── 🚀 run.py                     # ملف التشغيل السريع مع البيانات التجريبية
├── 📋 requirements.txt           # متطلبات المشروع
├── 📖 README.md                  # وصف المشروع (هذا الملف)
├── 📘 تعليمات_التشغيل.md         # دليل التشغيل المفصل
├── 📂 database/                  # إدارة قاعدة البيانات
│   └── 🗄️ db_manager.py          # إعداد وإدارة قاعدة البيانات
├── 📂 ui/                        # واجهات المستخدم
│   ├── 🏠 dashboard.py           # اللوحة الرئيسية والتنقل
│   ├── 👥 customer_ui.py         # واجهة إدارة العملاء
│   ├── 🔧 order_ui.py            # واجهة طلبات الصيانة
│   ├── 📦 inventory_ui.py        # واجهة إدارة المخزون
│   └── 📊 report_ui.py           # واجهة التقارير والإحصائيات
├── 📂 models/                    # نماذج البيانات
│   ├── 👤 customer.py            # نموذج العميل
│   ├── 🛠️ order.py               # نموذج طلب الصيانة
│   ├── 📦 inventory.py           # نموذج المخزون
│   └── 💰 payment.py             # نموذج المدفوعات
└── 📂 utils/                     # أدوات مساعدة
    ├── 📤 export.py              # تصدير البيانات
    └── 🔧 helpers.py             # دوال مساعدة
```

## 🗃️ قاعدة البيانات

النظام يستخدم SQLite مع الجداول التالية:

| الجدول | الوصف | الحقول الرئيسية |
|--------|--------|-----------------|
| **customers** | بيانات العملاء | id, name, phone, email, address |
| **orders** | طلبات الصيانة | id, customer_id, device_brand, device_model, issue, status |
| **inventory** | المخزون وقطع الغيار | id, name, part_number, quantity, price, low_stock_threshold |
| **payments** | المدفوعات | id, order_id, amount, paid_at |
| **expenses** | المصروفات | id, date, description, amount |
| **settings** | إعدادات النظام | key, value |

## 🎯 البيانات التجريبية

عند التشغيل الأول باستخدام `run.py`، سيقوم النظام بإنشاء بيانات تجريبية تشمل:

- **5 عملاء تجريبيين** مع بيانات كاملة
- **8 قطع غيار** في المخزون بكميات مختلفة
- **5 طلبات صيانة** بحالات متنوعة (في الانتظار، قيد الإصلاح، مكتمل)
- **3 مدفوعات** مرتبطة بالطلبات
- **4 مصروفات** تشغيلية

## 🖼️ لقطات الشاشة

### اللوحة الرئيسية
- شريط أدوات سريع للوصول للأقسام
- إحصائيات فورية
- شريط حالة مع الوقت الحالي

### إدارة العملاء
- جدول شامل لجميع العملاء
- نوافذ إضافة وتعديل سهلة الاستخدام
- بحث فوري أثناء الكتابة

### طلبات الصيانة
- تتبع مرئي لحالة الطلبات
- ربط تلقائي بالعملاء
- إدارة المدفوعات المرتبطة

### المخزون
- تنبيهات ملونة للمخزون المنخفض
- تحديث سريع للكميات
- تقارير القيمة الإجمالية

## 🔧 استكشاف الأخطاء

### ❌ مشكلة: خطأ في استيراد الوحدات
```bash
# الحل: تثبيت المتطلبات
pip install -r requirements.txt
```

### ❌ مشكلة: خطأ في قاعدة البيانات
```bash
# الحل: حذف قاعدة البيانات وإعادة إنشائها
rm phone_repair_shop.db
python run.py
```

### ❌ مشكلة: واجهة المستخدم لا تظهر
```bash
# على Ubuntu/Debian
sudo apt-get install python3-tk

# على CentOS/RHEL
sudo yum install tkinter
```

### ❌ مشكلة: الخطوط العربية لا تظهر
- تأكد من وجود خطوط عربية على النظام
- قم بتثبيت خط Arial أو أي خط عربي آخر

## 🚀 التطوير والتخصيص

### إضافة ميزات جديدة
1. **أضف الواجهة:** إنشاء ملف جديد في `ui/`
2. **أضف النموذج:** إنشاء ملف في `models/`
3. **حدث قاعدة البيانات:** تعديل `database/db_manager.py`
4. **ربط بالواجهة الرئيسية:** تحديث `dashboard.py`

### تخصيص الألوان والثيمات
```python
# الألوان المستخدمة في النظام
COLORS = {
    'primary': '#2c3e50',      # اللون الأساسي الداكن
    'blue': '#3498db',         # الأزرق
    'green': '#27ae60',        # الأخضر
    'red': '#e74c3c',          # الأحمر
    'orange': '#f39c12',       # البرتقالي
    'purple': '#9b59b6',       # البنفسجي
    'gray': '#95a5a6',         # الرمادي
    'background': '#f8f9fa'    # خلفية فاتحة
}
```

### إضافة لغات جديدة
- عدل النصوص في ملفات الواجهة
- أضف ملفات ترجمة في مجلد `locales/`
- حدث دالة `get_text()` في `utils/helpers.py`

## 📈 خطط التطوير المستقبلية

### الإصدار 2.0
- [ ] واجهة ويب للوصول عن بُعد
- [ ] تطبيق موبايل مصاحب
- [ ] تكامل مع أنظمة المحاسبة
- [ ] نظام إشعارات SMS/Email

### الإصدار 1.5
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تصدير التقارير إلى Excel/PDF
- [ ] نظام صلاحيات المستخدمين
- [ ] دعم الباركود وQR Code

### تحسينات قريبة
- [ ] رسوم بيانية تفاعلية
- [ ] تحسين الأداء للبيانات الكبيرة
- [ ] دعم قواعد بيانات خارجية
- [ ] واجهة أكثر حداثة

## 🤝 المساهمة في المشروع

نرحب بمساهماتكم في تطوير هذا المشروع:

1. **Fork** المشروع
2. إنشاء **branch** جديد للميزة
3. **Commit** التغييرات
4. **Push** إلى البرانش
5. إنشاء **Pull Request**

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. 📖 راجع ملف `تعليمات_التشغيل.md`
2. 🔍 ابحث في التعليقات داخل الكود
3. 📧 تواصل معنا عبر Issues في GitHub
4. 💬 انضم لمجتمع المطورين

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح تحت رخصة MIT. يمكنك استخدامه وتعديله وتوزيعه بحرية.

## 🙏 شكر وتقدير

- شكر خاص لمجتمع Python العربي
- مكتبة Tkinter لواجهة المستخدم
- SQLite لقاعدة البيانات الموثوقة
- جميع المساهمين في تطوير هذا المشروع

---

**📝 ملاحظة مهمة:** هذا النظام مصمم للاستخدام المحلي في محلات الصيانة الصغيرة والمتوسطة. للاستخدام في بيئات إنتاج كبيرة، يُنصح بإجراء تعديلات إضافية على الأمان وقاعدة البيانات.

**🔄 آخر تحديث:** يناير 2024
**📊 الإصدار:** 1.0.0
**👨‍💻 المطور:** فريق تطوير أنظمة إدارة المحلات
