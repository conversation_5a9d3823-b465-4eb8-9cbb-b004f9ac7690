"""
واجهة إدارة طلبات الصيانة العصرية 2025
Modern Order Management UI 2025
"""
import tkinter as tk
from tkinter import ttk, messagebox

class ModernOrderFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8fafc')
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6', 
            'accent': '#06b6d4',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'dark': '#1f2937',
            'gray': '#6b7280',
            'light_gray': '#f3f4f6',
            'white': '#ffffff'
        }
        self.create_modern_widgets()

    def create_modern_widgets(self):
        """إنشاء عناصر الواجهة العصرية"""
        # الشريط العلوي
        top_frame = tk.Frame(self, bg='white', padx=30, pady=25)
        top_frame.pack(fill='x', padx=30, pady=(30, 0))
        
        # العنوان الرئيسي
        title_label = tk.Label(top_frame, text="🔧 إدارة طلبات الصيانة", 
                              font=('Segoe UI', 24, 'bold'),
                              bg='white', fg=self.colors['dark'])
        title_label.pack(side='left')
        
        # زر إضافة طلب جديد
        add_btn = tk.Button(top_frame, text="➕ طلب جديد",
                           font=('Segoe UI', 12, 'bold'),
                           bg=self.colors['primary'], fg='white',
                           relief='flat', padx=25, pady=12,
                           command=self.add_order_dialog)
        add_btn.pack(side='right')
        
        # الوصف
        desc_label = tk.Label(top_frame, text="متابعة وإدارة جميع طلبات الصيانة والإصلاح",
                             font=('Segoe UI', 11),
                             bg='white', fg=self.colors['gray'])
        desc_label.pack(anchor='w', pady=(10, 0))
        
        # بطاقات الإحصائيات
        stats_frame = tk.Frame(self, bg='#f8fafc')
        stats_frame.pack(fill='x', padx=30, pady=20)
        
        # بطاقات الحالة
        stats_data = [
            ("⏳", "في الانتظار", "5", self.colors['warning']),
            ("🔧", "قيد الإصلاح", "8", self.colors['primary']),
            ("✅", "مكتملة", "12", self.colors['success']),
            ("📦", "تم التسليم", "20", self.colors['accent'])
        ]
        
        for icon, title, count, color in stats_data:
            card = tk.Frame(stats_frame, bg='white', relief='flat', bd=0, padx=20, pady=15)
            card.pack(side='left', fill='x', expand=True, padx=5)
            
            tk.Label(card, text=icon, font=('Segoe UI Emoji', 24),
                    bg='white', fg=color).pack()
            
            tk.Label(card, text=count, font=('Segoe UI', 20, 'bold'),
                    bg='white', fg=self.colors['dark']).pack()
            
            tk.Label(card, text=title, font=('Segoe UI', 11),
                    bg='white', fg=self.colors['gray']).pack()
        
        # منطقة الجدول
        table_frame = tk.Frame(self, bg='white', padx=30, pady=20)
        table_frame.pack(fill='both', expand=True, padx=30, pady=(15, 30))
        
        # عنوان الجدول
        tk.Label(table_frame, text="📋 قائمة طلبات الصيانة", 
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w', pady=(0, 15))
        
        # جدول الطلبات
        columns = ("id", "customer", "device", "issue", "status", "date")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        headers = {
            "id": "رقم الطلب",
            "customer": "العميل",
            "device": "الجهاز",
            "issue": "المشكلة",
            "status": "الحالة",
            "date": "التاريخ"
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col])
            self.tree.column(col, width=150)
        
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # أزرار العمليات
        buttons_frame = tk.Frame(table_frame, bg='white')
        buttons_frame.pack(fill='x', pady=(15, 0))
        
        buttons = [
            ("✏️ تعديل", self.edit_order, self.colors['warning']),
            ("🔄 تحديث الحالة", self.update_status, self.colors['primary']),
            ("🗑️ حذف", self.delete_order, self.colors['danger']),
            ("🖨️ طباعة", self.print_order, self.colors['secondary'])
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(buttons_frame, text=text, command=command,
                           font=('Segoe UI', 10, 'bold'),
                           bg=color, fg='white', relief='flat',
                           padx=15, pady=8)
            btn.pack(side='left', padx=5)

    def add_order_dialog(self):
        """نافذة إضافة طلب جديد"""
        messagebox.showinfo("طلب جديد", "نافذة إضافة طلب جديد قيد التطوير")

    def edit_order(self):
        """تعديل الطلب"""
        messagebox.showinfo("تعديل", "نافذة تعديل الطلب قيد التطوير")

    def update_status(self):
        """تحديث حالة الطلب"""
        messagebox.showinfo("تحديث الحالة", "نافذة تحديث الحالة قيد التطوير")

    def delete_order(self):
        """حذف الطلب"""
        messagebox.showinfo("حذف", "وظيفة حذف الطلب قيد التطوير")

    def print_order(self):
        """طباعة الطلب"""
        messagebox.showinfo("طباعة", "وظيفة طباعة الطلب قيد التطوير")
