"""
لوحة التحكم الرئيسية لنظام إدارة محل صيانة الجوالات
Dashboard and main window UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# إضافة المسارات
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

try:
    from ui.customer_ui import CustomerFrame
    from ui.order_ui import OrderFrame
    from ui.report_ui import ReportFrame
    from ui.inventory_ui import InventoryFrame
    from database.db_manager import get_connection
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")

class DashboardApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.create_styles()
        self.create_menu()
        self.create_toolbar()
        self.create_status_bar()
        self.frames = {}
        self.show_frame("customers")
        self.update_status()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.title("نظام إدارة محل صيانة الجوالات - Phone Repair Shop Management")
        self.geometry("1200x800")
        self.resizable(True, True)
        self.configure(bg='#f0f0f0')

        # تعيين أيقونة النافذة (إذا كانت متوفرة)
        try:
            self.iconbitmap('assets/icon.ico')
        except:
            pass

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def create_styles(self):
        """إنشاء الأنماط المخصصة"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # تخصيص الألوان
        self.style.configure('Title.TLabel',
                           font=('Arial', 16, 'bold'),
                           background='#2c3e50',
                           foreground='white')

    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self, font=('Arial', 10))
        self.config(menu=menubar)

        # قائمة الأقسام
        sections_menu = tk.Menu(menubar, tearoff=0, font=('Arial', 10))
        sections_menu.add_command(label="العملاء", command=lambda: self.show_frame("customers"))
        sections_menu.add_command(label="طلبات الصيانة", command=lambda: self.show_frame("orders"))
        sections_menu.add_command(label="المخزون", command=lambda: self.show_frame("inventory"))
        sections_menu.add_command(label="التقارير", command=lambda: self.show_frame("reports"))
        sections_menu.add_separator()
        sections_menu.add_command(label="إعدادات", command=self.show_settings)
        menubar.add_cascade(label="الأقسام", menu=sections_menu)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0, font=('Arial', 10))
        tools_menu.add_command(label="نسخ احتياطي", command=self.backup_database)
        tools_menu.add_command(label="استعادة النسخة الاحتياطية", command=self.restore_database)
        tools_menu.add_separator()
        tools_menu.add_command(label="تصدير البيانات", command=self.export_data)
        menubar.add_cascade(label="الأدوات", menu=tools_menu)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0, font=('Arial', 10))
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        menubar.add_cascade(label="مساعدة", menu=help_menu)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = tk.Frame(self, bg='#34495e', height=50)
        toolbar.pack(fill='x', side='top')
        toolbar.pack_propagate(False)

        # أزرار سريعة
        buttons = [
            ("العملاء", lambda: self.show_frame("customers"), "#3498db"),
            ("طلبات الصيانة", lambda: self.show_frame("orders"), "#e74c3c"),
            ("المخزون", lambda: self.show_frame("inventory"), "#f39c12"),
            ("التقارير", lambda: self.show_frame("reports"), "#27ae60"),
        ]

        for text, command, color in buttons:
            btn = tk.Button(toolbar, text=text, command=command,
                          bg=color, fg='white', font=('Arial', 10, 'bold'),
                          relief='flat', padx=20, pady=5,
                          activebackground=color, activeforeground='white')
            btn.pack(side='left', padx=5, pady=10)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Frame(self, bg='#2c3e50', height=25)
        self.status_bar.pack(fill='x', side='bottom')
        self.status_bar.pack_propagate(False)

        self.status_label = tk.Label(self.status_bar, text="جاهز",
                                   bg='#2c3e50', fg='white', font=('Arial', 9))
        self.status_label.pack(side='left', padx=10)

        self.time_label = tk.Label(self.status_bar, text="",
                                 bg='#2c3e50', fg='white', font=('Arial', 9))
        self.time_label.pack(side='right', padx=10)

    def update_status(self):
        """تحديث شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.after(1000, self.update_status)

    def show_frame(self, name):
        """عرض الإطار المحدد"""
        # إخفاء جميع الإطارات
        for frame in self.frames.values():
            frame.pack_forget()

        # عرض الإطار المطلوب
        if name == "customers":
            if "customers" not in self.frames:
                self.frames["customers"] = CustomerFrame(self)
            self.frames["customers"].pack(fill="both", expand=True)
            self.status_label.config(text="قسم العملاء")

        elif name == "orders":
            if "orders" not in self.frames:
                self.frames["orders"] = OrderFrame(self)
            self.frames["orders"].pack(fill="both", expand=True)
            self.status_label.config(text="قسم طلبات الصيانة")

        elif name == "inventory":
            if "inventory" not in self.frames:
                self.frames["inventory"] = InventoryFrame(self)
            self.frames["inventory"].pack(fill="both", expand=True)
            self.status_label.config(text="قسم المخزون")

        elif name == "reports":
            if "reports" not in self.frames:
                self.frames["reports"] = ReportFrame(self)
            self.frames["reports"].pack(fill="both", expand=True)
            self.status_label.config(text="قسم التقارير")

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        messagebox.showinfo("الإعدادات", "نافذة الإعدادات قيد التطوير")

    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        messagebox.showinfo("نسخ احتياطي", "تم إنشاء النسخة الاحتياطية بنجاح")

    def restore_database(self):
        """استعادة النسخة الاحتياطية"""
        messagebox.showinfo("استعادة", "تم استعادة النسخة الاحتياطية بنجاح")

    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير", "تم تصدير البيانات بنجاح")

    def show_help(self):
        """عرض دليل المستخدم"""
        help_text = """
        دليل المستخدم - نظام إدارة محل صيانة الجوالات

        الأقسام الرئيسية:
        1. العملاء: إدارة بيانات العملاء
        2. طلبات الصيانة: متابعة طلبات الإصلاح
        3. المخزون: إدارة قطع الغيار
        4. التقارير: عرض الإحصائيات والتقارير

        للمساعدة الإضافية، يرجى الاتصال بالدعم الفني.
        """
        messagebox.showinfo("دليل المستخدم", help_text)

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
        نظام إدارة محل صيانة الجوالات
        الإصدار 1.0

        تم تطويره باستخدام Python و Tkinter

        جميع الحقوق محفوظة © 2024
        """
        messagebox.showinfo("حول البرنامج", about_text)
