"""
Dashboard and main window UI
"""
import tkinter as tk
from ui.customer_ui import CustomerFrame
from ui.order_ui import OrderFrame
from ui.report_ui import ReportFrame

class DashboardApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Phone Repair Shop Management System")
        self.geometry("1000x700")
        self.resizable(True, True)
        self.create_menu()
        self.frames = {}
        self.show_frame("customers")

    def create_menu(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)
        section_menu = tk.Menu(menubar, tearoff=0)
        section_menu.add_command(label="Customers", command=lambda: self.show_frame("customers"))
        section_menu.add_command(label="Orders", command=lambda: self.show_frame("orders"))
        section_menu.add_command(label="Reports", command=lambda: self.show_frame("reports"))
        menubar.add_cascade(label="Sections", menu=section_menu)

    def show_frame(self, name):
        for frame in self.frames.values():
            frame.pack_forget()
        if name == "customers":
            if "customers" not in self.frames:
                self.frames["customers"] = CustomerFrame(self)
            self.frames["customers"].pack(fill="both", expand=True)
        elif name == "orders":
            if "orders" not in self.frames:
                self.frames["orders"] = OrderFrame(self)
            self.frames["orders"].pack(fill="both", expand=True)
        elif name == "reports":
            if "reports" not in self.frames:
                self.frames["reports"] = ReportFrame(self)
            self.frames["reports"].pack(fill="both", expand=True)
