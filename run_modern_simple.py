#!/usr/bin/env python3
"""
ملف تشغيل الواجهة العصرية 2025 - نسخة مبسطة
Modern UI 2025 Launcher - Simple Version
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def setup_paths():
    """إعداد المسارات"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # إضافة المجلدات للمسار
    paths_to_add = [
        current_dir,
        os.path.join(current_dir, 'ui'),
        os.path.join(current_dir, 'database'),
        os.path.join(current_dir, 'models'),
        os.path.join(current_dir, 'utils')
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.append(path)

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        from database.db_manager import get_connection
        
        conn = get_connection()
        c = conn.cursor()
        
        # فحص وجود بيانات
        c.execute("SELECT COUNT(*) FROM customers")
        customer_count = c.fetchone()[0]
        
        if customer_count == 0:
            # إضافة عملاء تجريبيين
            sample_customers = [
                ("أحمد محمد العلي", "0501234567", "<EMAIL>", "الرياض، حي النخيل"),
                ("فاطمة سعد الأحمد", "0509876543", "<EMAIL>", "جدة، حي الصفا"),
                ("محمد عبدالله السعد", "0551234567", "<EMAIL>", "الدمام، حي الفيصلية"),
                ("نورا خالد المحمد", "0561234567", "<EMAIL>", "مكة المكرمة، حي العزيزية"),
                ("خالد عبدالرحمن القحطاني", "0571234567", "<EMAIL>", "المدينة المنورة، حي قباء")
            ]
            
            for customer in sample_customers:
                c.execute("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)", customer)
            
            # إضافة قطع غيار تجريبية
            sample_inventory = [
                ("شاشة iPhone 14 Pro", "IP14P-SCR-001", 15, 650.00, 3),
                ("بطارية Samsung Galaxy S23", "SG23-BAT-001", 20, 180.00, 5),
                ("كاميرا خلفية iPhone 13", "IP13-CAM-001", 12, 320.00, 2),
                ("شاحن سريع USB-C 65W", "CHG-TC-65W", 30, 85.00, 10),
                ("واقي شاشة زجاجي مقوى", "SCR-PROT-GLASS", 100, 25.00, 20)
            ]
            
            for item in sample_inventory:
                c.execute("INSERT INTO inventory (name, part_number, quantity, price, low_stock_threshold) VALUES (?, ?, ?, ?, ?)", item)
            
            # إضافة طلبات صيانة تجريبية
            sample_orders = [
                (1, "Apple", "iPhone 14 Pro", "شاشة مكسورة", 700.00, "في الانتظار", "أحمد الفني", "2024-01-20 10:30:00"),
                (2, "Samsung", "Galaxy S23", "بطارية لا تشحن", 200.00, "قيد الإصلاح", "محمد الفني", "2024-01-19 14:20:00"),
                (3, "Apple", "iPhone 13", "كاميرا لا تعمل", 350.00, "مكتمل", "أحمد الفني", "2024-01-18 09:15:00")
            ]
            
            for order in sample_orders:
                c.execute("INSERT INTO orders (customer_id, device_brand, device_model, issue, estimated_cost, status, technician, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", order)
            
            # إضافة مدفوعات تجريبية
            sample_payments = [
                (3, 350.00, "2024-01-18 17:30:00"),
                (1, 350.00, "2024-01-20 12:00:00")
            ]
            
            for payment in sample_payments:
                c.execute("INSERT INTO payments (order_id, amount, paid_at) VALUES (?, ?, ?)", payment)
            
            conn.commit()
            print("✅ تم إنشاء البيانات التجريبية بنجاح")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 " + "=" * 60)
    print("🚀 نظام إدارة محل صيانة الجوالات - الواجهة العصرية 2025")
    print("🚀 Phone Repair Shop Management System - Modern UI 2025")
    print("🚀 " + "=" * 60)
    
    # إعداد المسارات
    setup_paths()
    
    try:
        # تهيئة قاعدة البيانات
        print("🗄️ تهيئة قاعدة البيانات...")
        from database.db_manager import init_db
        init_db()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # إنشاء بيانات تجريبية
        print("📊 إنشاء البيانات التجريبية...")
        create_sample_data()
        
        # تشغيل التطبيق العصري
        print("🎨 تشغيل الواجهة العصرية...")
        
        # إنشاء نافذة رئيسية مبسطة
        root = tk.Tk()
        root.title("نظام إدارة محل صيانة الجوالات 2025")
        root.geometry("1400x900")
        root.configure(bg='#0f0f23')
        
        # توسيط النافذة
        root.update_idletasks()
        width = 1400
        height = 900
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f'{width}x{height}+{x}+{y}')
        
        # إنشاء الواجهة العصرية
        from ui.modern_dashboard import ModernDashboardApp
        
        # إغلاق النافذة المؤقتة وإنشاء التطبيق
        root.destroy()
        
        app = ModernDashboardApp()
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎉 استمتع بالواجهة العصرية الجديدة!")
        app.mainloop()
        
    except ImportError as e:
        error_msg = f"❌ خطأ في استيراد الوحدات: {e}"
        print(error_msg)
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        
    except Exception as e:
        error_msg = f"❌ حدث خطأ أثناء تشغيل التطبيق: {e}"
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
