"""
Reports and analytics UI
"""
import tkinter as tk
from tkinter import ttk
from database.db_manager import get_connection

class ReportFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.create_widgets()

    def create_widgets(self):
        tk.Label(self, text="Reports & Analytics", font=("Arial", 18, "bold")).pack(pady=10)
        btn_frame = tk.Frame(self)
        btn_frame.pack(pady=5)
        tk.Button(btn_frame, text="Daily Income", command=self.show_daily_income).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Monthly Income", command=self.show_monthly_income).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Expenses", command=self.show_expenses).pack(side=tk.LEFT, padx=5)
        tk.But<PERSON>(btn_frame, text="Top Customers", command=self.show_top_customers).pack(side=tk.LEFT, padx=5)
        self.tree = ttk.Treeview(self, columns=("col1", "col2", "col3"), show="headings")
        self.tree.pack(fill="both", expand=True, pady=10)

    def show_daily_income(self):
        self.tree.delete(*self.tree.get_children())
        conn = get_connection()
        c = conn.cursor()
        c.execute("SELECT paid_at, SUM(amount) FROM payments GROUP BY paid_at")
        self.tree['columns'] = ("Date", "Total Income", "-")
        for row in c.fetchall():
            self.tree.insert('', 'end', values=(row[0], row[1], ""))
        conn.close()

    def show_monthly_income(self):
        self.tree.delete(*self.tree.get_children())
        conn = get_connection()
        c = conn.cursor()
        c.execute("SELECT substr(paid_at,1,7) as month, SUM(amount) FROM payments GROUP BY month")
        self.tree['columns'] = ("Month", "Total Income", "-")
        for row in c.fetchall():
            self.tree.insert('', 'end', values=(row[0], row[1], ""))
        conn.close()

    def show_expenses(self):
        self.tree.delete(*self.tree.get_children())
        conn = get_connection()
        c = conn.cursor()
        c.execute("SELECT date, description, amount FROM expenses")
        self.tree['columns'] = ("Date", "Description", "Amount")
        for row in c.fetchall():
            self.tree.insert('', 'end', values=row)
        conn.close()

    def show_top_customers(self):
        self.tree.delete(*self.tree.get_children())
        conn = get_connection()
        c = conn.cursor()
        c.execute("SELECT customers.name, COUNT(orders.id) as repairs FROM customers JOIN orders ON customers.id=orders.customer_id GROUP BY customers.id ORDER BY repairs DESC LIMIT 10")
        self.tree['columns'] = ("Customer", "Repairs", "-")
        for row in c.fetchall():
            self.tree.insert('', 'end', values=(row[0], row[1], ""))
        conn.close()
