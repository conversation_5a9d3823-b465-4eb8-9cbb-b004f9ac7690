"""
واجهة التقارير والإحصائيات
Reports and analytics UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class ReportFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8f9fa')
        self.create_widgets()
        self.show_dashboard()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="التقارير والإحصائيات",
                font=("Arial", 20, "bold"),
                bg='#2c3e50', fg='white').pack(pady=15)

        # أزرار التقارير
        btn_frame = tk.Frame(self, bg='#f8f9fa')
        btn_frame.pack(pady=10, padx=20, fill='x')

        # الصف الأول من الأزرار
        btn_row1 = tk.Frame(btn_frame, bg='#f8f9fa')
        btn_row1.pack(fill='x', pady=5)

        buttons_row1 = [
            ("لوحة المعلومات", self.show_dashboard, "#3498db"),
            ("الإيرادات اليومية", self.show_daily_income, "#27ae60"),
            ("الإيرادات الشهرية", self.show_monthly_income, "#2ecc71"),
            ("المصروفات", self.show_expenses, "#e74c3c"),
        ]

        for text, command, color in buttons_row1:
            tk.Button(btn_row1, text=text, command=command,
                     bg=color, fg='white', font=("Arial", 11, "bold"),
                     padx=15, pady=8).pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        btn_row2 = tk.Frame(btn_frame, bg='#f8f9fa')
        btn_row2.pack(fill='x', pady=5)

        buttons_row2 = [
            ("أفضل العملاء", self.show_top_customers, "#9b59b6"),
            ("تقرير المخزون", self.show_inventory_report, "#f39c12"),
            ("تقرير الفنيين", self.show_technician_report, "#34495e"),
            ("تصدير التقارير", self.export_reports, "#95a5a6"),
        ]

        for text, command, color in buttons_row2:
            tk.Button(btn_row2, text=text, command=command,
                     bg=color, fg='white', font=("Arial", 11, "bold"),
                     padx=15, pady=8).pack(side=tk.LEFT, padx=5)

        # إطار المحتوى الرئيسي
        self.content_frame = tk.Frame(self, bg='#f8f9fa')
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # جدول البيانات
        self.tree_frame = tk.Frame(self.content_frame, bg='#f8f9fa')
        self.tree_frame.pack(fill="both", expand=True)

        self.tree = ttk.Treeview(self.tree_frame, show="headings", height=15)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(self.tree_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # إطار الرسوم البيانية
        self.chart_frame = tk.Frame(self.content_frame, bg='#f8f9fa')

        # إطار الإحصائيات السريعة
        self.stats_frame = tk.Frame(self, bg='#ecf0f1', relief='ridge', bd=2)
        self.stats_frame.pack(fill='x', padx=20, pady=(0, 10))

    def show_dashboard(self):
        """عرض لوحة المعلومات الرئيسية"""
        self.clear_content()

        # إخفاء الرسوم البيانية وإظهار الجدول
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            # إحصائيات عامة
            c.execute("SELECT COUNT(*) FROM customers")
            total_customers = c.fetchone()[0]

            c.execute("SELECT COUNT(*) FROM orders")
            total_orders = c.fetchone()[0]

            c.execute("SELECT COUNT(*) FROM orders WHERE status = 'في الانتظار'")
            pending_orders = c.fetchone()[0]

            c.execute("SELECT COUNT(*) FROM orders WHERE status = 'مكتمل'")
            completed_orders = c.fetchone()[0]

            c.execute("SELECT SUM(amount) FROM payments")
            total_revenue = c.fetchone()[0] or 0

            c.execute("SELECT SUM(amount) FROM expenses")
            total_expenses = c.fetchone()[0] or 0

            # إعداد الجدول
            columns = ("metric", "value", "description")
            self.tree['columns'] = columns

            headers = {
                "metric": "المؤشر",
                "value": "القيمة",
                "description": "الوصف"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                if col == "metric":
                    self.tree.column(col, width=200)
                elif col == "value":
                    self.tree.column(col, width=150, anchor='center')
                else:
                    self.tree.column(col, width=300)

            # إدراج البيانات
            dashboard_data = [
                ("إجمالي العملاء", total_customers, "العدد الكلي للعملاء المسجلين"),
                ("إجمالي طلبات الصيانة", total_orders, "العدد الكلي لطلبات الصيانة"),
                ("طلبات في الانتظار", pending_orders, "طلبات الصيانة التي لم تكتمل بعد"),
                ("طلبات مكتملة", completed_orders, "طلبات الصيانة المكتملة"),
                ("إجمالي الإيرادات", f"{total_revenue:.2f} ريال", "مجموع المدفوعات المستلمة"),
                ("إجمالي المصروفات", f"{total_expenses:.2f} ريال", "مجموع المصروفات المسجلة"),
                ("صافي الربح", f"{total_revenue - total_expenses:.2f} ريال", "الإيرادات - المصروفات"),
            ]

            for row in dashboard_data:
                self.tree.insert('', 'end', values=row)

            # تحديث الإحصائيات السريعة
            self.update_stats_frame(total_customers, total_orders, total_revenue, total_expenses)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل لوحة المعلومات:\n{str(e)}")

    def show_daily_income(self):
        """عرض الإيرادات اليومية"""
        self.clear_content()
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("""
                SELECT DATE(paid_at) as date, SUM(amount) as total
                FROM payments
                GROUP BY DATE(paid_at)
                ORDER BY date DESC
                LIMIT 30
            """)

            # إعداد الجدول
            columns = ("date", "total", "orders_count")
            self.tree['columns'] = columns

            headers = {
                "date": "التاريخ",
                "total": "إجمالي الإيرادات",
                "orders_count": "عدد الطلبات"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                self.tree.column(col, width=200, anchor='center')

            # إدراج البيانات
            total_income = 0
            for row in c.fetchall():
                date, amount = row

                # حساب عدد الطلبات في هذا التاريخ
                c.execute("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?", (date,))
                orders_count = c.fetchone()[0]

                self.tree.insert('', 'end', values=(date, f"{amount:.2f} ريال", orders_count))
                total_income += amount

            # إضافة صف الإجمالي
            self.tree.insert('', 'end', values=("الإجمالي", f"{total_income:.2f} ريال", ""))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الإيرادات اليومية:\n{str(e)}")

    def show_monthly_income(self):
        """عرض الإيرادات الشهرية"""
        self.clear_content()
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("""
                SELECT strftime('%Y-%m', paid_at) as month, SUM(amount) as total
                FROM payments
                GROUP BY strftime('%Y-%m', paid_at)
                ORDER BY month DESC
                LIMIT 12
            """)

            # إعداد الجدول
            columns = ("month", "total", "avg_daily")
            self.tree['columns'] = columns

            headers = {
                "month": "الشهر",
                "total": "إجمالي الإيرادات",
                "avg_daily": "متوسط يومي"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                self.tree.column(col, width=200, anchor='center')

            # إدراج البيانات
            total_income = 0
            for row in c.fetchall():
                month, amount = row
                avg_daily = amount / 30  # تقريبي

                self.tree.insert('', 'end', values=(month, f"{amount:.2f} ريال", f"{avg_daily:.2f} ريال"))
                total_income += amount

            # إضافة صف الإجمالي
            self.tree.insert('', 'end', values=("الإجمالي", f"{total_income:.2f} ريال", ""))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الإيرادات الشهرية:\n{str(e)}")

    def show_expenses(self):
        """عرض المصروفات"""
        self.clear_content()
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("SELECT date, description, amount FROM expenses ORDER BY date DESC")

            # إعداد الجدول
            columns = ("date", "description", "amount")
            self.tree['columns'] = columns

            headers = {
                "date": "التاريخ",
                "description": "الوصف",
                "amount": "المبلغ"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                if col == "description":
                    self.tree.column(col, width=300)
                else:
                    self.tree.column(col, width=150, anchor='center')

            # إدراج البيانات
            total_expenses = 0
            for row in c.fetchall():
                date, description, amount = row
                self.tree.insert('', 'end', values=(date, description, f"{amount:.2f} ريال"))
                total_expenses += amount

            # إضافة صف الإجمالي
            self.tree.insert('', 'end', values=("", "إجمالي المصروفات", f"{total_expenses:.2f} ريال"))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المصروفات:\n{str(e)}")

    def show_top_customers(self):
        """عرض أفضل العملاء"""
        self.clear_content()
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("""
                SELECT c.name, c.phone, COUNT(o.id) as repairs,
                       COALESCE(SUM(p.amount), 0) as total_paid
                FROM customers c
                LEFT JOIN orders o ON c.id = o.customer_id
                LEFT JOIN payments p ON o.id = p.order_id
                GROUP BY c.id
                HAVING repairs > 0
                ORDER BY repairs DESC, total_paid DESC
                LIMIT 20
            """)

            # إعداد الجدول
            columns = ("name", "phone", "repairs", "total_paid")
            self.tree['columns'] = columns

            headers = {
                "name": "اسم العميل",
                "phone": "رقم الهاتف",
                "repairs": "عدد الطلبات",
                "total_paid": "إجمالي المدفوعات"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                if col == "name":
                    self.tree.column(col, width=200)
                elif col == "phone":
                    self.tree.column(col, width=150, anchor='center')
                else:
                    self.tree.column(col, width=150, anchor='center')

            # إدراج البيانات
            for row in c.fetchall():
                name, phone, repairs, total_paid = row
                self.tree.insert('', 'end', values=(name, phone, repairs, f"{total_paid:.2f} ريال"))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات العملاء:\n{str(e)}")

    def show_inventory_report(self):
        """عرض تقرير المخزون"""
        self.clear_content()
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("""
                SELECT name, part_number, quantity, price,
                       low_stock_threshold, (quantity * price) as total_value
                FROM inventory
                ORDER BY total_value DESC
            """)

            # إعداد الجدول
            columns = ("name", "part_number", "quantity", "price", "status", "total_value")
            self.tree['columns'] = columns

            headers = {
                "name": "اسم القطعة",
                "part_number": "رقم القطعة",
                "quantity": "الكمية",
                "price": "السعر",
                "status": "الحالة",
                "total_value": "القيمة الإجمالية"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                if col == "name":
                    self.tree.column(col, width=200)
                else:
                    self.tree.column(col, width=120, anchor='center')

            # إدراج البيانات
            total_inventory_value = 0
            low_stock_count = 0

            for row in c.fetchall():
                name, part_number, quantity, price, threshold, total_value = row

                # تحديد حالة المخزون
                if quantity == 0:
                    status = "نفد المخزون"
                    low_stock_count += 1
                elif quantity <= threshold:
                    status = "مخزون منخفض"
                    low_stock_count += 1
                else:
                    status = "متوفر"

                self.tree.insert('', 'end', values=(
                    name, part_number, quantity, f"{price:.2f}", status, f"{total_value:.2f}"
                ))
                total_inventory_value += total_value

            # إضافة صف الإجمالي
            self.tree.insert('', 'end', values=(
                "الإجمالي", "", "", "", f"تنبيهات: {low_stock_count}", f"{total_inventory_value:.2f} ريال"
            ))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل تقرير المخزون:\n{str(e)}")

    def show_technician_report(self):
        """عرض تقرير الفنيين"""
        self.clear_content()
        self.chart_frame.pack_forget()
        self.tree_frame.pack(fill="both", expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("""
                SELECT technician,
                       COUNT(*) as total_orders,
                       SUM(CASE WHEN status = 'مكتمل' THEN 1 ELSE 0 END) as completed,
                       SUM(CASE WHEN status = 'في الانتظار' THEN 1 ELSE 0 END) as pending,
                       AVG(estimated_cost) as avg_cost
                FROM orders
                WHERE technician IS NOT NULL AND technician != ''
                GROUP BY technician
                ORDER BY total_orders DESC
            """)

            # إعداد الجدول
            columns = ("technician", "total_orders", "completed", "pending", "completion_rate", "avg_cost")
            self.tree['columns'] = columns

            headers = {
                "technician": "اسم الفني",
                "total_orders": "إجمالي الطلبات",
                "completed": "مكتملة",
                "pending": "في الانتظار",
                "completion_rate": "معدل الإنجاز",
                "avg_cost": "متوسط التكلفة"
            }

            for col in columns:
                self.tree.heading(col, text=headers[col])
                if col == "technician":
                    self.tree.column(col, width=150)
                else:
                    self.tree.column(col, width=120, anchor='center')

            # إدراج البيانات
            for row in c.fetchall():
                technician, total, completed, pending, avg_cost = row
                completion_rate = (completed / total * 100) if total > 0 else 0

                self.tree.insert('', 'end', values=(
                    technician, total, completed, pending,
                    f"{completion_rate:.1f}%", f"{avg_cost:.2f} ريال"
                ))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل تقرير الفنيين:\n{str(e)}")

    def export_reports(self):
        """تصدير التقارير"""
        messagebox.showinfo("تصدير التقارير", "ميزة تصدير التقارير قيد التطوير\nسيتم إضافتها في التحديث القادم")

    def clear_content(self):
        """مسح المحتوى الحالي"""
        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        # مسح الإحصائيات
        for widget in self.stats_frame.winfo_children():
            widget.destroy()

    def update_stats_frame(self, customers, orders, revenue, expenses):
        """تحديث إطار الإحصائيات السريعة"""
        self.clear_stats_frame()

        stats_inner = tk.Frame(self.stats_frame, bg='#ecf0f1')
        stats_inner.pack(pady=10)

        stats_data = [
            ("العملاء", customers, "#3498db"),
            ("الطلبات", orders, "#e74c3c"),
            ("الإيرادات", f"{revenue:.0f}", "#27ae60"),
            ("المصروفات", f"{expenses:.0f}", "#f39c12"),
        ]

        for label, value, color in stats_data:
            stat_frame = tk.Frame(stats_inner, bg='#ecf0f1')
            stat_frame.pack(side=tk.LEFT, padx=20)

            tk.Label(stat_frame, text=str(value), font=("Arial", 16, "bold"),
                    bg='#ecf0f1', fg=color).pack()
            tk.Label(stat_frame, text=label, font=("Arial", 10),
                    bg='#ecf0f1', fg='#2c3e50').pack()

    def clear_stats_frame(self):
        """مسح إطار الإحصائيات"""
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
