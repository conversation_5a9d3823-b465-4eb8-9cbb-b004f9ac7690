# تعليمات تشغيل نظام إدارة محل صيانة الجوالات

## نظرة عامة
نظام شامل لإدارة محل صيانة الجوالات باللغة العربية يتضمن إدارة العملاء، طلبات الصيانة، المخزون، والتقارير.

## متطلبات النظام

### المتطلبات الأساسية
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

### المكتبات المطلوبة
المكتبات الأساسية (مدمجة مع Python):
- tkinter (واجهة المستخدم)
- sqlite3 (قاعدة البيانات)
- datetime (التاريخ والوقت)
- re (التعبيرات النمطية)

المكتبات الإضافية (اختيارية):
```bash
pip install matplotlib reportlab pillow openpyxl pandas numpy
```

## طرق التشغيل

### الطريقة الأولى: التشغيل السريع
```bash
python run.py
```

### الطريقة الثانية: التشغيل المباشر
```bash
python main.py
```

### الطريقة الثالثة: من خلال مجلد المشروع
```bash
cd "اختبار تطبيق حسابات python"
python -m main
```

## الميزات الرئيسية

### 1. إدارة العملاء
- إضافة عملاء جدد
- تعديل بيانات العملاء
- البحث في قاعدة العملاء
- عرض سجل طلبات كل عميل
- طباعة بيانات العميل

### 2. إدارة طلبات الصيانة
- إنشاء طلبات صيانة جديدة
- تتبع حالة الطلبات (في الانتظار، قيد الإصلاح، مكتمل، تم التسليم)
- تحديث حالة الطلبات
- إضافة المدفوعات
- طباعة تفاصيل الطلب

### 3. إدارة المخزون
- إضافة قطع غيار جديدة
- تحديث كميات المخزون
- تنبيهات المخزون المنخفض
- تقارير المخزون الشاملة

### 4. التقارير والإحصائيات
- لوحة معلومات شاملة
- تقارير الإيرادات اليومية والشهرية
- تقارير المصروفات
- تقارير أفضل العملاء
- تقارير أداء الفنيين

## بنية المشروع

```
اختبار تطبيق حسابات python/
├── main.py                 # نقطة التشغيل الرئيسية
├── run.py                  # ملف التشغيل السريع
├── requirements.txt        # متطلبات المشروع
├── README.md              # وصف المشروع
├── تعليمات_التشغيل.md     # هذا الملف
├── database/              # إدارة قاعدة البيانات
│   └── db_manager.py
├── ui/                    # واجهات المستخدم
│   ├── dashboard.py       # اللوحة الرئيسية
│   ├── customer_ui.py     # واجهة العملاء
│   ├── order_ui.py        # واجهة طلبات الصيانة
│   ├── inventory_ui.py    # واجهة المخزون
│   └── report_ui.py       # واجهة التقارير
├── models/                # نماذج البيانات
│   ├── customer.py
│   ├── order.py
│   ├── inventory.py
│   └── payment.py
└── utils/                 # أدوات مساعدة
    ├── export.py
    └── helpers.py
```

## قاعدة البيانات

النظام يستخدم SQLite مع الجداول التالية:
- `customers` - بيانات العملاء
- `orders` - طلبات الصيانة
- `inventory` - المخزون وقطع الغيار
- `payments` - المدفوعات
- `expenses` - المصروفات
- `settings` - إعدادات النظام

## البيانات التجريبية

عند التشغيل الأول، سيقوم النظام بإنشاء بيانات تجريبية تشمل:
- 5 عملاء تجريبيين
- 8 قطع غيار في المخزون
- 5 طلبات صيانة بحالات مختلفة
- 3 مدفوعات
- 4 مصروفات

## استكشاف الأخطاء

### مشكلة: خطأ في استيراد الوحدات
**الحل:**
```bash
pip install -r requirements.txt
```

### مشكلة: خطأ في قاعدة البيانات
**الحل:**
- احذف ملف `phone_repair_shop.db`
- أعد تشغيل التطبيق

### مشكلة: واجهة المستخدم لا تظهر بشكل صحيح
**الحل:**
- تأكد من تثبيت tkinter
- على Ubuntu/Debian: `sudo apt-get install python3-tk`

### مشكلة: الخطوط العربية لا تظهر
**الحل:**
- تأكد من وجود خطوط عربية على النظام
- قم بتثبيت خط Arial أو خط عربي آخر

## التطوير والتخصيص

### إضافة ميزات جديدة
1. أضف الواجهة في مجلد `ui/`
2. أضف النموذج في مجلد `models/`
3. حدث قاعدة البيانات في `database/db_manager.py`
4. أضف الواجهة للوحة الرئيسية في `dashboard.py`

### تخصيص الألوان والثيمات
عدل الألوان في ملفات الواجهة:
- `#2c3e50` - اللون الأساسي الداكن
- `#3498db` - الأزرق
- `#27ae60` - الأخضر
- `#e74c3c` - الأحمر
- `#f39c12` - البرتقالي

## الدعم والمساعدة

للحصول على المساعدة:
1. راجع هذا الملف أولاً
2. تحقق من ملف `README.md`
3. راجع التعليقات في الكود
4. تأكد من تثبيت جميع المتطلبات

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله حسب الحاجة.

---

**ملاحظة:** هذا النظام مصمم للاستخدام المحلي وقد يحتاج تعديلات إضافية للاستخدام في بيئة إنتاج.
