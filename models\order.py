"""
Repair Order model and logic
"""
class RepairOrder:
    def __init__(self, id, customer_id, device_brand, device_model, issue, estimated_cost, status, technician, created_at, delivered_at):
        self.id = id
        self.customer_id = customer_id
        self.device_brand = device_brand
        self.device_model = device_model
        self.issue = issue
        self.estimated_cost = estimated_cost
        self.status = status
        self.technician = technician
        self.created_at = created_at
        self.delivered_at = delivered_at

    def __repr__(self):
        return f"RepairOrder({self.id}, {self.device_brand}, {self.status})"
