"""
إدارة قاعدة البيانات (SQLite) لتخزين السجل
"""
import sqlite3
from datetime import datetime

DB_NAME = 'calorie_history.db'


def init_db():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT,
        age INTEGER,
        weight REAL,
        height REAL,
        gender TEXT,
        activity_level TEXT,
        goal TEXT,
        calories REAL
    )''')
    conn.commit()
    conn.close()


def save_result(age, weight, height, gender, activity_level, goal, calories):
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute('''INSERT INTO history (date, age, weight, height, gender, activity_level, goal, calories)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
              (datetime.now().strftime('%Y-%m-%d %H:%M'), age, weight, height, gender, activity_level, goal, calories))
    conn.commit()
    conn.close()


def get_history():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute('SELECT * FROM history ORDER BY date DESC')
    rows = c.fetchall()
    conn.close()
    return rows

# ...existing code...
