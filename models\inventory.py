"""
Inventory item model and logic
"""
class InventoryItem:
    def __init__(self, id, name, part_number, quantity, price, low_stock_threshold):
        self.id = id
        self.name = name
        self.part_number = part_number
        self.quantity = quantity
        self.price = price
        self.low_stock_threshold = low_stock_threshold

    def __repr__(self):
        return f"InventoryItem({self.id}, {self.name}, {self.quantity})"
