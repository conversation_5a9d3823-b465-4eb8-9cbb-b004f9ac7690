"""
نقطة تشغيل نظام إدارة محل صيانة الجوالات
Phone Repair Shop Management System
"""
from tkinter import messagebox
import sys
import os

# إضافة المجلدات للمسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'ui'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'models'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from ui.dashboard import DashboardApp
from database.db_manager import init_db

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        # تهيئة قاعدة البيانات
        init_db()

        # إنشاء وتشغيل التطبيق
        app = DashboardApp()
        app.mainloop()

    except Exception as e:
        messagebox.showerror("خطأ في التشغيل", f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
        print(f"Error: {e}")

if __name__ == '__main__':
    main()
