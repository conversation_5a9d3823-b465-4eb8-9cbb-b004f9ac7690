#!/usr/bin/env python3
"""
ملف تشغيل سريع لنظام إدارة محل صيانة الجوالات
Quick run file for Phone Repair Shop Management System
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """فحص المتطلبات الأساسية"""
    required_modules = ['tkinter', 'sqlite3', 'datetime', 're']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"المتطلبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    return True

def setup_paths():
    """إعداد المسارات"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # إضافة المجلدات للمسار
    paths_to_add = [
        current_dir,
        os.path.join(current_dir, 'ui'),
        os.path.join(current_dir, 'database'),
        os.path.join(current_dir, 'models'),
        os.path.join(current_dir, 'utils')
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.append(path)

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        from database.db_manager import get_connection
        
        conn = get_connection()
        c = conn.cursor()
        
        # فحص وجود بيانات
        c.execute("SELECT COUNT(*) FROM customers")
        customer_count = c.fetchone()[0]
        
        if customer_count == 0:
            # إضافة عملاء تجريبيين
            sample_customers = [
                ("أحمد محمد", "0501234567", "<EMAIL>", "الرياض، حي النخيل"),
                ("فاطمة علي", "0509876543", "<EMAIL>", "جدة، حي الصفا"),
                ("محمد سعد", "0551234567", "<EMAIL>", "الدمام، حي الفيصلية"),
                ("نورا أحمد", "0561234567", "<EMAIL>", "مكة، حي العزيزية"),
                ("خالد عبدالله", "0571234567", "<EMAIL>", "المدينة، حي قباء")
            ]
            
            for customer in sample_customers:
                c.execute("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)", customer)
            
            # إضافة قطع غيار تجريبية
            sample_inventory = [
                ("شاشة iPhone 13", "IP13-SCR-001", 10, 450.00, 3),
                ("بطارية Samsung Galaxy S21", "SG21-BAT-001", 15, 120.00, 5),
                ("كاميرا خلفية iPhone 12", "IP12-CAM-001", 8, 280.00, 2),
                ("شاحن سريع Type-C", "CHG-TC-001", 25, 35.00, 10),
                ("واقي شاشة زجاجي", "SCR-PROT-001", 50, 15.00, 20),
                ("سماعة داخلية iPhone", "IP-SPK-001", 12, 85.00, 4),
                ("فليكس الشحن Samsung", "SG-FLX-001", 20, 45.00, 8),
                ("كاميرا أمامية Huawei", "HW-FCAM-001", 6, 95.00, 3)
            ]
            
            for item in sample_inventory:
                c.execute("INSERT INTO inventory (name, part_number, quantity, price, low_stock_threshold) VALUES (?, ?, ?, ?, ?)", item)
            
            # إضافة طلبات صيانة تجريبية
            sample_orders = [
                (1, "Apple", "iPhone 13", "شاشة مكسورة", 500.00, "في الانتظار", "أحمد الفني", "2024-01-15 10:30:00"),
                (2, "Samsung", "Galaxy S21", "بطارية لا تشحن", 150.00, "قيد الإصلاح", "محمد الفني", "2024-01-14 14:20:00"),
                (3, "Apple", "iPhone 12", "كاميرا لا تعمل", 320.00, "مكتمل", "أحمد الفني", "2024-01-13 09:15:00"),
                (4, "Huawei", "P30 Pro", "مشكلة في الصوت", 180.00, "تم التسليم", "سعد الفني", "2024-01-12 16:45:00"),
                (5, "Samsung", "Galaxy Note 20", "شاشة سوداء", 420.00, "في الانتظار", "محمد الفني", "2024-01-16 11:00:00")
            ]
            
            for order in sample_orders:
                c.execute("INSERT INTO orders (customer_id, device_brand, device_model, issue, estimated_cost, status, technician, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", order)
            
            # إضافة مدفوعات تجريبية
            sample_payments = [
                (3, 320.00, "2024-01-13 17:30:00"),
                (4, 180.00, "2024-01-12 18:00:00"),
                (1, 250.00, "2024-01-15 12:00:00")  # دفعة جزئية
            ]
            
            for payment in sample_payments:
                c.execute("INSERT INTO payments (order_id, amount, paid_at) VALUES (?, ?, ?)", payment)
            
            # إضافة مصروفات تجريبية
            sample_expenses = [
                ("2024-01-10", "شراء قطع غيار", 1500.00),
                ("2024-01-08", "فاتورة كهرباء", 280.00),
                ("2024-01-05", "أدوات صيانة", 450.00),
                ("2024-01-03", "إيجار المحل", 2000.00)
            ]
            
            for expense in sample_expenses:
                c.execute("INSERT INTO expenses (date, description, amount) VALUES (?, ?, ?)", expense)
            
            conn.commit()
            print("تم إنشاء البيانات التجريبية بنجاح")
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ في إنشاء البيانات التجريبية: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("نظام إدارة محل صيانة الجوالات")
    print("Phone Repair Shop Management System")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        print("يرجى تثبيت المتطلبات المفقودة أولاً")
        return
    
    # إعداد المسارات
    setup_paths()
    
    try:
        # تهيئة قاعدة البيانات
        from database.db_manager import init_db
        init_db()
        print("تم تهيئة قاعدة البيانات بنجاح")
        
        # إنشاء بيانات تجريبية
        create_sample_data()
        
        # تشغيل التطبيق
        from ui.dashboard import DashboardApp
        
        print("جاري تشغيل التطبيق...")
        app = DashboardApp()
        app.mainloop()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد الوحدات: {e}"
        print(error_msg)
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        
    except Exception as e:
        error_msg = f"حدث خطأ أثناء تشغيل التطبيق: {e}"
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
