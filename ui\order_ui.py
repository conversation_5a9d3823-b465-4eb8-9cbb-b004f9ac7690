"""
Repair order management UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class OrderFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.create_widgets()
        self.load_orders()

    def create_widgets(self):
        tk.Label(self, text="Repair Orders", font=("Arial", 18, "bold")).pack(pady=10)
        btn_frame = tk.Frame(self)
        btn_frame.pack(pady=5)
        tk.Button(btn_frame, text="New Order", command=self.add_order_dialog).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Edit", command=self.edit_order_dialog).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Delete", command=self.delete_order).pack(side=tk.LEFT, padx=5)
        self.tree = ttk.Treeview(self, columns=("id", "customer_id", "device_brand", "device_model", "issue", "estimated_cost", "status", "technician", "created_at", "delivered_at"), show="headings")
        for col in self.tree['columns']:
            self.tree.heading(col, text=col.title())
            self.tree.column(col, width=100)
        self.tree.pack(fill="both", expand=True, pady=10)

    def load_orders(self):
        for row in self.tree.get_children():
            self.tree.delete(row)
        conn = get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM orders")
        for row in c.fetchall():
            self.tree.insert('', 'end', values=row)
        conn.close()

    def add_order_dialog(self):
        self.order_dialog()

    def edit_order_dialog(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Select", "Please select an order to edit.")
            return
        values = self.tree.item(selected[0], 'values')
        self.order_dialog(values)

    def order_dialog(self, values=None):
        win = tk.Toplevel(self)
        win.title("Add/Edit Order")
        fields = ["Customer ID", "Device Brand", "Device Model", "Issue", "Estimated Cost", "Status", "Technician", "Created At", "Delivered At"]
        defaults = [values[i+1] if values else "" for i in range(9)]
        vars = [tk.StringVar(value=defaults[i]) for i in range(9)]
        for i, field in enumerate(fields):
            tk.Label(win, text=field).grid(row=i, column=0, padx=5, pady=5)
            tk.Entry(win, textvariable=vars[i]).grid(row=i, column=1, padx=5, pady=5)
        def save():
            data = [v.get() for v in vars]
            conn = get_connection()
            c = conn.cursor()
            if values:
                c.execute("UPDATE orders SET customer_id=?, device_brand=?, device_model=?, issue=?, estimated_cost=?, status=?, technician=?, created_at=?, delivered_at=? WHERE id=?", (*data, values[0]))
            else:
                c.execute("INSERT INTO orders (customer_id, device_brand, device_model, issue, estimated_cost, status, technician, created_at, delivered_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", data)
            conn.commit()
            conn.close()
            self.load_orders()
            win.destroy()
        tk.Button(win, text="Save", command=save).grid(row=9, column=0, columnspan=2, pady=10)

    def delete_order(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Select", "Please select an order to delete.")
            return
        values = self.tree.item(selected[0], 'values')
        if messagebox.askyesno("Delete", f"Delete order {values[0]}?"):
            conn = get_connection()
            c = conn.cursor()
            c.execute("DELETE FROM orders WHERE id=?", (values[0],))
            conn.commit()
            conn.close()
            self.load_orders()
