"""
واجهة إدارة طلبات الصيانة والإصلاح
Repair order management UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class OrderFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8f9fa')
        self.create_widgets()
        self.load_orders()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="إدارة طلبات الصيانة والإصلاح",
                font=("Arial", 20, "bold"),
                bg='#2c3e50', fg='white').pack(pady=15)

        # إطار البحث والفلترة
        search_frame = tk.Frame(self, bg='#f8f9fa')
        search_frame.pack(pady=10, padx=20, fill='x')

        # البحث
        search_left = tk.Frame(search_frame, bg='#f8f9fa')
        search_left.pack(side=tk.LEFT)

        tk.Label(search_left, text="البحث:", font=("Arial", 12),
                bg='#f8f9fa').pack(side=tk.LEFT, padx=(0, 5))

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_left, textvariable=self.search_var,
                               font=("Arial", 11), width=20)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind('<KeyRelease>', lambda e: self.search_orders())

        # فلتر الحالة
        tk.Label(search_left, text="الحالة:", font=("Arial", 12),
                bg='#f8f9fa').pack(side=tk.LEFT, padx=(20, 5))

        self.status_filter_var = tk.StringVar(value="الكل")
        status_combo = ttk.Combobox(search_left, textvariable=self.status_filter_var,
                                   values=["الكل", "في الانتظار", "قيد الإصلاح", "مكتمل", "تم التسليم", "ملغي"],
                                   state="readonly", width=15)
        status_combo.pack(side=tk.LEFT, padx=5)
        status_combo.bind('<<ComboboxSelected>>', lambda e: self.search_orders())

        tk.Button(search_left, text="بحث", command=self.search_orders,
                 bg='#3498db', fg='white', font=("Arial", 10, "bold"),
                 padx=15).pack(side=tk.LEFT, padx=5)

        tk.Button(search_left, text="إعادة تحميل", command=self.load_orders,
                 bg='#95a5a6', fg='white', font=("Arial", 10, "bold"),
                 padx=15).pack(side=tk.LEFT, padx=5)

        # أزرار الإضافة والعمليات
        search_right = tk.Frame(search_frame, bg='#f8f9fa')
        search_right.pack(side=tk.RIGHT)

        tk.Button(search_right, text="طلب صيانة جديد", command=self.add_order_dialog,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=20, pady=5).pack(side=tk.RIGHT, padx=5)

        # جدول طلبات الصيانة
        table_frame = tk.Frame(self, bg='#f8f9fa')
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إعداد الجدول
        columns = ("id", "customer_name", "device_info", "issue", "estimated_cost",
                  "status", "technician", "created_at", "priority")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        headers = {
            "id": "رقم الطلب",
            "customer_name": "العميل",
            "device_info": "الجهاز",
            "issue": "المشكلة",
            "estimated_cost": "التكلفة المقدرة",
            "status": "الحالة",
            "technician": "الفني",
            "created_at": "تاريخ الطلب",
            "priority": "الأولوية"
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == "id":
                self.tree.column(col, width=80, anchor='center')
            elif col == "customer_name":
                self.tree.column(col, width=120)
            elif col == "device_info":
                self.tree.column(col, width=150)
            elif col == "issue":
                self.tree.column(col, width=200)
            elif col == "estimated_cost":
                self.tree.column(col, width=100, anchor='center')
            elif col == "status":
                self.tree.column(col, width=100, anchor='center')
            elif col == "technician":
                self.tree.column(col, width=100)
            elif col == "created_at":
                self.tree.column(col, width=120, anchor='center')
            elif col == "priority":
                self.tree.column(col, width=80, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # أزرار العمليات
        btn_frame = tk.Frame(self, bg='#f8f9fa')
        btn_frame.pack(pady=10)

        buttons = [
            ("تعديل الطلب", self.edit_order_dialog, "#f39c12"),
            ("تحديث الحالة", self.update_status_dialog, "#3498db"),
            ("حذف الطلب", self.delete_order, "#e74c3c"),
            ("طباعة الطلب", self.print_order, "#9b59b6"),
            ("إضافة دفعة", self.add_payment_dialog, "#27ae60")
        ]

        for text, command, color in buttons:
            tk.Button(btn_frame, text=text, command=command,
                     bg=color, fg='white', font=("Arial", 11, "bold"),
                     padx=15, pady=5).pack(side=tk.LEFT, padx=5)

        # إحصائيات سريعة
        stats_frame = tk.Frame(self, bg='#ecf0f1', relief='ridge', bd=2)
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))

        stats_inner = tk.Frame(stats_frame, bg='#ecf0f1')
        stats_inner.pack(pady=10)

        self.total_orders_label = tk.Label(stats_inner, text="إجمالي الطلبات: 0",
                                          font=("Arial", 12, "bold"),
                                          bg='#ecf0f1', fg='#2c3e50')
        self.total_orders_label.pack(side=tk.LEFT, padx=20)

        self.pending_orders_label = tk.Label(stats_inner, text="في الانتظار: 0",
                                            font=("Arial", 12, "bold"),
                                            bg='#ecf0f1', fg='#e67e22')
        self.pending_orders_label.pack(side=tk.LEFT, padx=20)

        self.completed_orders_label = tk.Label(stats_inner, text="مكتملة: 0",
                                              font=("Arial", 12, "bold"),
                                              bg='#ecf0f1', fg='#27ae60')
        self.completed_orders_label.pack(side=tk.LEFT, padx=20)

        self.total_revenue_label = tk.Label(stats_inner, text="الإيرادات: 0",
                                           font=("Arial", 12, "bold"),
                                           bg='#ecf0f1', fg='#8e44ad')
        self.total_revenue_label.pack(side=tk.LEFT, padx=20)

    def load_orders(self, search=None, status_filter=None):
        """تحميل بيانات طلبات الصيانة"""
        # مسح البيانات الحالية
        for row in self.tree.get_children():
            self.tree.delete(row)

        try:
            conn = get_connection()
            c = conn.cursor()

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT o.id, c.name as customer_name,
                       (o.device_brand || ' ' || o.device_model) as device_info,
                       o.issue, o.estimated_cost, o.status, o.technician,
                       o.created_at, 'عادية' as priority
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                WHERE 1=1
            """
            params = []

            if search:
                query += " AND (c.name LIKE ? OR o.device_brand LIKE ? OR o.device_model LIKE ? OR o.issue LIKE ?)"
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param, search_param])

            if status_filter and status_filter != "الكل":
                query += " AND o.status = ?"
                params.append(status_filter)

            query += " ORDER BY o.created_at DESC"

            c.execute(query, params)
            orders = c.fetchall()

            # إحصائيات
            total_orders = len(orders)
            pending_count = 0
            completed_count = 0
            total_revenue = 0

            for row in orders:
                self.tree.insert('', 'end', values=row)

                # حساب الإحصائيات
                status = row[5]  # status column
                cost = row[4] if row[4] else 0  # estimated_cost column

                if status in ["في الانتظار", "قيد الإصلاح"]:
                    pending_count += 1
                elif status in ["مكتمل", "تم التسليم"]:
                    completed_count += 1
                    total_revenue += float(cost) if cost else 0

            # تحديث الإحصائيات
            self.total_orders_label.config(text=f"إجمالي الطلبات: {total_orders}")
            self.pending_orders_label.config(text=f"في الانتظار: {pending_count}")
            self.completed_orders_label.config(text=f"مكتملة: {completed_count}")
            self.total_revenue_label.config(text=f"الإيرادات: {total_revenue:.2f}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def search_orders(self):
        """البحث وفلترة طلبات الصيانة"""
        search_term = self.search_var.get().strip()
        status_filter = self.status_filter_var.get()
        self.load_orders(
            search_term if search_term else None,
            status_filter if status_filter != "الكل" else None
        )

    def add_order_dialog(self):
        """فتح نافذة إضافة طلب صيانة جديد"""
        self.order_dialog()

    def edit_order_dialog(self):
        """فتح نافذة تعديل طلب الصيانة المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد طلب للتعديل.")
            return
        values = self.tree.item(selected[0], 'values')
        self.order_dialog(values)

    def order_dialog(self, values=None):
        """نافذة إضافة/تعديل طلب الصيانة"""
        win = tk.Toplevel(self)
        win.title("طلب صيانة جديد" if not values else "تعديل طلب الصيانة")
        win.geometry("600x700")
        win.resizable(False, False)
        win.configure(bg='#f8f9fa')

        # توسيط النافذة
        win.transient(self)
        win.grab_set()

        # العنوان
        title_text = "طلب صيانة جديد" if not values else "تعديل طلب الصيانة"
        tk.Label(win, text=title_text, font=("Arial", 18, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # إطار التمرير
        canvas = tk.Canvas(win, bg='#f8f9fa')
        scrollbar = ttk.Scrollbar(win, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#f8f9fa')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إطار الحقول
        fields_frame = tk.Frame(scrollable_frame, bg='#f8f9fa')
        fields_frame.pack(pady=20, padx=40, fill='both', expand=True)

        self.field_vars = {}

        # اختيار العميل
        tk.Label(fields_frame, text="العميل:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').grid(row=0, column=0, sticky='e', padx=10, pady=10)

        self.customer_var = tk.StringVar()
        customer_combo = ttk.Combobox(fields_frame, textvariable=self.customer_var,
                                     width=30, state="readonly")
        customer_combo.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        # تحميل قائمة العملاء
        try:
            conn = get_connection()
            c = conn.cursor()
            c.execute("SELECT id, name FROM customers ORDER BY name")
            customers = c.fetchall()
            customer_values = [f"{customer[0]} - {customer[1]}" for customer in customers]
            customer_combo['values'] = customer_values
            conn.close()
        except:
            pass

        # باقي الحقول
        fields = [
            ("ماركة الجهاز:", "device_brand"),
            ("موديل الجهاز:", "device_model"),
            ("وصف المشكلة:", "issue"),
            ("التكلفة المقدرة:", "estimated_cost"),
            ("الفني المسؤول:", "technician")
        ]

        for i, (label_text, field_name) in enumerate(fields, 1):
            tk.Label(fields_frame, text=label_text, font=("Arial", 12, "bold"),
                    bg='#f8f9fa', fg='#2c3e50').grid(row=i, column=0, sticky='e', padx=10, pady=10)

            if field_name == "issue":
                # حقل نص متعدد الأسطر للمشكلة
                text_widget = tk.Text(fields_frame, height=4, width=35, font=("Arial", 11))
                text_widget.grid(row=i, column=1, padx=10, pady=10, sticky='ew')
                self.field_vars[field_name] = text_widget
            else:
                var = tk.StringVar()
                entry = tk.Entry(fields_frame, textvariable=var, font=("Arial", 11), width=35)
                entry.grid(row=i, column=1, padx=10, pady=10, sticky='ew')
                self.field_vars[field_name] = var

        # حالة الطلب
        tk.Label(fields_frame, text="حالة الطلب:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').grid(row=len(fields)+1, column=0, sticky='e', padx=10, pady=10)

        self.status_var = tk.StringVar(value="في الانتظار")
        status_combo = ttk.Combobox(fields_frame, textvariable=self.status_var,
                                   values=["في الانتظار", "قيد الإصلاح", "مكتمل", "تم التسليم", "ملغي"],
                                   state="readonly", width=32)
        status_combo.grid(row=len(fields)+1, column=1, padx=10, pady=10, sticky='ew')

        # ملاحظات إضافية
        tk.Label(fields_frame, text="ملاحظات:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').grid(row=len(fields)+2, column=0, sticky='e', padx=10, pady=10)

        self.notes_text = tk.Text(fields_frame, height=3, width=35, font=("Arial", 11))
        self.notes_text.grid(row=len(fields)+2, column=1, padx=10, pady=10, sticky='ew')

        # تعبئة البيانات في حالة التعديل
        if values:
            # البحث عن العميل وتعيينه
            try:
                conn = get_connection()
                c = conn.cursor()
                c.execute("SELECT customer_id FROM orders WHERE id=?", (values[0],))
                customer_id = c.fetchone()[0]
                c.execute("SELECT name FROM customers WHERE id=?", (customer_id,))
                customer_name = c.fetchone()[0]
                self.customer_var.set(f"{customer_id} - {customer_name}")
                conn.close()
            except:
                pass

            # تعبئة باقي الحقول (يحتاج تحديث حسب بنية البيانات الفعلية)

        canvas.pack(side="left", fill="both", expand=True, padx=20)
        scrollbar.pack(side="right", fill="y")

        # أزرار الحفظ والإلغاء
        btn_frame = tk.Frame(win, bg='#f8f9fa')
        btn_frame.pack(pady=20)

        def save_order():
            """حفظ طلب الصيانة"""
            try:
                # استخراج معرف العميل
                customer_selection = self.customer_var.get()
                if not customer_selection:
                    messagebox.showerror("خطأ", "يرجى اختيار العميل")
                    return

                customer_id = customer_selection.split(" - ")[0]

                # جمع البيانات
                device_brand = self.field_vars["device_brand"].get().strip()
                device_model = self.field_vars["device_model"].get().strip()

                # الحصول على النص من Text widget
                if isinstance(self.field_vars["issue"], tk.Text):
                    issue = self.field_vars["issue"].get('1.0', 'end-1c').strip()
                else:
                    issue = self.field_vars["issue"].get().strip()

                estimated_cost = self.field_vars["estimated_cost"].get().strip()
                technician = self.field_vars["technician"].get().strip()
                status = self.status_var.get()
                notes = self.notes_text.get('1.0', 'end-1c').strip()

                # التحقق من صحة البيانات
                if not device_brand:
                    messagebox.showerror("خطأ", "يرجى إدخال ماركة الجهاز")
                    return

                if not device_model:
                    messagebox.showerror("خطأ", "يرجى إدخال موديل الجهاز")
                    return

                if not issue:
                    messagebox.showerror("خطأ", "يرجى وصف المشكلة")
                    return

                # التحقق من التكلفة
                try:
                    if estimated_cost:
                        float(estimated_cost)
                except ValueError:
                    messagebox.showerror("خطأ", "التكلفة المقدرة يجب أن تكون رقماً")
                    return

                # حفظ في قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                if values:  # تعديل
                    c.execute("""UPDATE orders
                               SET customer_id=?, device_brand=?, device_model=?, issue=?,
                                   estimated_cost=?, status=?, technician=?
                               WHERE id=?""",
                             (customer_id, device_brand, device_model, issue,
                              estimated_cost, status, technician, values[0]))
                    messagebox.showinfo("نجح", "تم تحديث طلب الصيانة بنجاح")
                else:  # إضافة جديد
                    c.execute("""INSERT INTO orders
                               (customer_id, device_brand, device_model, issue,
                                estimated_cost, status, technician, created_at)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                             (customer_id, device_brand, device_model, issue,
                              estimated_cost, status, technician, current_time))
                    messagebox.showinfo("نجح", "تم إضافة طلب الصيانة بنجاح")

                conn.commit()
                conn.close()

                # تحديث الجدول وإغلاق النافذة
                self.load_orders()
                win.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ البيانات:\n{str(e)}")

        tk.Button(btn_frame, text="حفظ", command=save_order,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

        tk.Button(btn_frame, text="إلغاء", command=win.destroy,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

    def update_status_dialog(self):
        """نافذة تحديث حالة الطلب"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد طلب لتحديث حالته.")
            return

        values = self.tree.item(selected[0], 'values')
        order_id = values[0]
        current_status = values[5]

        # إنشاء نافذة التحديث
        win = tk.Toplevel(self)
        win.title(f"تحديث حالة الطلب رقم {order_id}")
        win.geometry("400x300")
        win.resizable(False, False)
        win.configure(bg='#f8f9fa')

        # توسيط النافذة
        win.transient(self)
        win.grab_set()

        # العنوان
        tk.Label(win, text=f"تحديث حالة الطلب رقم {order_id}",
                font=("Arial", 16, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # الحالة الحالية
        tk.Label(win, text=f"الحالة الحالية: {current_status}",
                font=("Arial", 12),
                bg='#f8f9fa', fg='#7f8c8d').pack(pady=10)

        # الحالة الجديدة
        tk.Label(win, text="الحالة الجديدة:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=(20, 5))

        status_var = tk.StringVar(value=current_status)
        status_combo = ttk.Combobox(win, textvariable=status_var,
                                   values=["في الانتظار", "قيد الإصلاح", "مكتمل", "تم التسليم", "ملغي"],
                                   state="readonly", width=25)
        status_combo.pack(pady=5)

        # ملاحظات التحديث
        tk.Label(win, text="ملاحظات التحديث:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=(20, 5))

        notes_text = tk.Text(win, height=4, width=40, font=("Arial", 11))
        notes_text.pack(pady=5, padx=20)

        def update_status():
            """تحديث حالة الطلب"""
            try:
                new_status = status_var.get()
                notes = notes_text.get('1.0', 'end-1c').strip()

                if new_status == current_status:
                    messagebox.showinfo("تنبيه", "لم يتم تغيير الحالة")
                    return

                # تحديث قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # تحديث الحالة
                c.execute("UPDATE orders SET status=? WHERE id=?", (new_status, order_id))

                # إذا كانت الحالة "تم التسليم"، تحديث تاريخ التسليم
                if new_status == "تم التسليم":
                    c.execute("UPDATE orders SET delivered_at=? WHERE id=?", (update_time, order_id))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم تحديث حالة الطلب إلى: {new_status}")
                self.load_orders()
                win.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في التحديث:\n{str(e)}")

        # أزرار التحديث والإلغاء
        btn_frame = tk.Frame(win, bg='#f8f9fa')
        btn_frame.pack(pady=20)

        tk.Button(btn_frame, text="تحديث", command=update_status,
                 bg='#3498db', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

        tk.Button(btn_frame, text="إلغاء", command=win.destroy,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

    def delete_order(self):
        """حذف طلب الصيانة المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد طلب للحذف.")
            return

        values = self.tree.item(selected[0], 'values')
        order_id = values[0]
        customer_name = values[1]

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف طلب الصيانة رقم {order_id}؟\n"
                              f"العميل: {customer_name}\n"
                              "سيتم حذف جميع المدفوعات المرتبطة به أيضاً."):
            try:
                conn = get_connection()
                c = conn.cursor()

                # حذف المدفوعات المرتبطة أولاً
                c.execute("DELETE FROM payments WHERE order_id=?", (order_id,))

                # حذف الطلب
                c.execute("DELETE FROM orders WHERE id=?", (order_id,))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم حذف طلب الصيانة رقم {order_id} بنجاح")
                self.load_orders()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الطلب:\n{str(e)}")

    def print_order(self):
        """طباعة تفاصيل طلب الصيانة"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد طلب للطباعة.")
            return

        values = self.tree.item(selected[0], 'values')
        order_id = values[0]

        try:
            # الحصول على تفاصيل الطلب الكاملة
            conn = get_connection()
            c = conn.cursor()
            c.execute("""
                SELECT o.*, c.name, c.phone, c.email, c.address
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                WHERE o.id = ?
            """, (order_id,))

            order_details = c.fetchone()
            conn.close()

            if not order_details:
                messagebox.showerror("خطأ", "لم يتم العثور على تفاصيل الطلب")
                return

            # إنشاء نافذة معاينة الطباعة
            print_win = tk.Toplevel(self)
            print_win.title(f"معاينة الطباعة - طلب رقم {order_id}")
            print_win.geometry("700x600")
            print_win.configure(bg='white')

            # محتوى الطباعة
            content = f"""
            طلب صيانة رقم: {order_details[0]}
            ═══════════════════════════════════════

            بيانات العميل:
            الاسم: {order_details[10] or 'غير محدد'}
            الهاتف: {order_details[11] or 'غير محدد'}
            البريد الإلكتروني: {order_details[12] or 'غير محدد'}
            العنوان: {order_details[13] or 'غير محدد'}

            ═══════════════════════════════════════

            تفاصيل الجهاز:
            الماركة: {order_details[2] or 'غير محدد'}
            الموديل: {order_details[3] or 'غير محدد'}

            وصف المشكلة:
            {order_details[4] or 'غير محدد'}

            ═══════════════════════════════════════

            تفاصيل الصيانة:
            التكلفة المقدرة: {order_details[5] or '0'} ريال
            الحالة: {order_details[6] or 'غير محدد'}
            الفني المسؤول: {order_details[7] or 'غير محدد'}

            تاريخ الطلب: {order_details[8] or 'غير محدد'}
            تاريخ التسليم: {order_details[9] or 'لم يتم التسليم بعد'}

            ═══════════════════════════════════════
            تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            text_widget = tk.Text(print_win, font=("Arial", 11), bg='white', wrap='word')
            text_widget.pack(fill='both', expand=True, padx=20, pady=20)
            text_widget.insert('1.0', content)
            text_widget.config(state='disabled')

            # أزرار الطباعة
            btn_frame = tk.Frame(print_win, bg='white')
            btn_frame.pack(pady=10)

            tk.Button(btn_frame, text="طباعة",
                     command=lambda: messagebox.showinfo("طباعة", "تم إرسال المستند للطباعة"),
                     bg='#3498db', fg='white', font=("Arial", 12, "bold"),
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)

            tk.Button(btn_frame, text="حفظ كـ PDF",
                     command=lambda: messagebox.showinfo("حفظ", "تم حفظ المستند كـ PDF"),
                     bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إعداد الطباعة:\n{str(e)}")

    def add_payment_dialog(self):
        """نافذة إضافة دفعة للطلب"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد طلب لإضافة دفعة له.")
            return

        values = self.tree.item(selected[0], 'values')
        order_id = values[0]
        customer_name = values[1]
        estimated_cost = values[4]

        # إنشاء نافذة الدفعة
        win = tk.Toplevel(self)
        win.title(f"إضافة دفعة - طلب رقم {order_id}")
        win.geometry("450x350")
        win.resizable(False, False)
        win.configure(bg='#f8f9fa')

        # توسيط النافذة
        win.transient(self)
        win.grab_set()

        # العنوان
        tk.Label(win, text=f"إضافة دفعة للطلب رقم {order_id}",
                font=("Arial", 16, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # معلومات الطلب
        info_frame = tk.Frame(win, bg='#ecf0f1', relief='ridge', bd=1)
        info_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(info_frame, text=f"العميل: {customer_name}",
                font=("Arial", 11), bg='#ecf0f1').pack(pady=5)
        tk.Label(info_frame, text=f"التكلفة المقدرة: {estimated_cost} ريال",
                font=("Arial", 11), bg='#ecf0f1').pack(pady=5)

        # حقول الدفعة
        fields_frame = tk.Frame(win, bg='#f8f9fa')
        fields_frame.pack(pady=20, padx=40)

        # مبلغ الدفعة
        tk.Label(fields_frame, text="مبلغ الدفعة:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').grid(row=0, column=0, sticky='e', padx=10, pady=10)

        amount_var = tk.StringVar()
        tk.Entry(fields_frame, textvariable=amount_var, font=("Arial", 12),
                width=20).grid(row=0, column=1, padx=10, pady=10)

        # طريقة الدفع
        tk.Label(fields_frame, text="طريقة الدفع:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').grid(row=1, column=0, sticky='e', padx=10, pady=10)

        payment_method_var = tk.StringVar(value="نقداً")
        method_combo = ttk.Combobox(fields_frame, textvariable=payment_method_var,
                                   values=["نقداً", "بطاقة ائتمان", "تحويل بنكي", "شيك"],
                                   state="readonly", width=18)
        method_combo.grid(row=1, column=1, padx=10, pady=10)

        # ملاحظات
        tk.Label(fields_frame, text="ملاحظات:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').grid(row=2, column=0, sticky='e', padx=10, pady=10)

        notes_text = tk.Text(fields_frame, height=3, width=25, font=("Arial", 11))
        notes_text.grid(row=2, column=1, padx=10, pady=10)

        def save_payment():
            """حفظ الدفعة"""
            try:
                amount = amount_var.get().strip()
                method = payment_method_var.get()
                notes = notes_text.get('1.0', 'end-1c').strip()

                if not amount:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ الدفعة")
                    return

                try:
                    amount_float = float(amount)
                    if amount_float <= 0:
                        messagebox.showerror("خطأ", "مبلغ الدفعة يجب أن يكون أكبر من صفر")
                        return
                except ValueError:
                    messagebox.showerror("خطأ", "مبلغ الدفعة يجب أن يكون رقماً صحيحاً")
                    return

                # حفظ في قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                payment_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                c.execute("""INSERT INTO payments (order_id, amount, paid_at)
                           VALUES (?, ?, ?)""",
                         (order_id, amount_float, payment_time))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم إضافة دفعة بمبلغ {amount} ريال بنجاح")
                win.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ الدفعة:\n{str(e)}")

        # أزرار الحفظ والإلغاء
        btn_frame = tk.Frame(win, bg='#f8f9fa')
        btn_frame.pack(pady=20)

        tk.Button(btn_frame, text="حفظ الدفعة", command=save_payment,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

        tk.Button(btn_frame, text="إلغاء", command=win.destroy,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)
