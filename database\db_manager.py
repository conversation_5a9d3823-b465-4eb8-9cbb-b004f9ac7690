"""
Database manager for Phone Repair Shop System
Handles all DB operations and connections (SQLite)
"""
import sqlite3
import os

DB_PATH = 'phone_repair_shop.db'

def get_connection():
    return sqlite3.connect(DB_PATH)

def init_db():
    conn = get_connection()
    c = conn.cursor()
    # Customers
    c.execute('''CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        phone TEXT,
        email TEXT,
        address TEXT
    )''')
    # Repair Orders
    c.execute('''CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER,
        device_brand TEXT,
        device_model TEXT,
        issue TEXT,
        estimated_cost REAL,
        status TEXT,
        technician TEXT,
        created_at TEXT,
        delivered_at TEXT,
        FOREIGN KEY(customer_id) REFERENCES customers(id)
    )''')
    # Payments
    c.execute('''CREATE TABLE IF NOT EXISTS payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER,
        amount REAL,
        paid_at TEXT,
        FOREIGN KEY(order_id) REFERENCES orders(id)
    )''')
    # Inventory
    c.execute('''CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        part_number TEXT,
        quantity INTEGER,
        price REAL,
        low_stock_threshold INTEGER
    )''')
    # Expenses
    c.execute('''CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        description TEXT,
        amount REAL,
        date TEXT
    )''')
    # Settings
    c.execute('''CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT
    )''')
    conn.commit()
    conn.close()
