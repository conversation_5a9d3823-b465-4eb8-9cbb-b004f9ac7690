#!/usr/bin/env python3
"""
🇪🇬 تطبيق إدارة محل صيانة الجوالات المصري العصري 2025
Egyptian Phone Repair Shop Management System 2025
أفضل تصميم عصري مع حواف دائرية وتفاصيل مثالية
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import sqlite3
import os
from datetime import datetime
import re

class تطبيق_الصيانة_المصري(tk.Tk):
    def __init__(self):
        super().__init__()
        self.إعداد_النافذة()
        self.إنشاء_قاعدة_البيانات()
        self.إنشاء_الألوان_والخطوط()
        self.إنشاء_الواجهة()
        self.تحميل_البيانات()
        self.تحديث_الوقت()

    def إعداد_النافذة(self):
        """إعداد النافذة الرئيسية بأفضل تصميم"""
        self.title("🇪🇬 تطبيق إدارة محل صيانة الجوالات المصري 2025")
        self.geometry("1400x900")
        self.configure(bg='#0a0e27')
        self.resizable(True, True)

        # توسيط النافذة
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - 700
        y = (self.winfo_screenheight() // 2) - 450
        self.geometry(f'1400x900+{x}+{y}')

        # تأثير الشفافية
        self.attributes('-alpha', 0.98)

    def إنشاء_الألوان_والخطوط(self):
        """إنشاء نظام الألوان المصري العصري والخطوط"""
        # ألوان مصرية عصرية مع تدرجات ذهبية
        self.ألوان = {
            'خلفية_رئيسية': '#0a0e27',      # أزرق داكن مصري
            'خلفية_ثانوية': '#1a1f3a',     # أزرق متوسط
            'ذهبي_مصري': '#d4af37',        # ذهبي مصري أصيل
            'ذهبي_فاتح': '#f4e4bc',        # ذهبي فاتح
            'أحمر_مصري': '#c41e3a',        # أحمر مصري
            'أخضر_نيلي': '#2e8b57',        # أخضر نيلي
            'أبيض_لؤلؤي': '#f8f8ff',       # أبيض لؤلؤي
            'رمادي_فضي': '#c0c0c0',        # رمادي فضي
            'أزرق_نيلي': '#191970',        # أزرق نيلي
            'بنفسجي_ملكي': '#6a0dad',      # بنفسجي ملكي
        }

        # خطوط عربية جميلة
        self.خطوط = {
            'عنوان_كبير': ('Arial', 24, 'bold'),
            'عنوان_متوسط': ('Arial', 18, 'bold'),
            'عنوان_صغير': ('Arial', 14, 'bold'),
            'نص_عادي': ('Arial', 12),
            'نص_صغير': ('Arial', 10),
            'أرقام': ('Courier New', 12, 'bold'),
        }

    def إنشاء_قاعدة_البيانات(self):
        """إنشاء قاعدة البيانات المتكاملة"""
        self.اتصال = sqlite3.connect('محل_الصيانة_المصري.db')
        self.مؤشر = self.اتصال.cursor()

        # جدول العملاء
        self.مؤشر.execute('''
            CREATE TABLE IF NOT EXISTS العملاء (
                الرقم INTEGER PRIMARY KEY AUTOINCREMENT,
                الاسم TEXT NOT NULL,
                الهاتف TEXT NOT NULL,
                الإيميل TEXT,
                العنوان TEXT,
                تاريخ_التسجيل TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول الطلبات
        self.مؤشر.execute('''
            CREATE TABLE IF NOT EXISTS الطلبات (
                رقم_الطلب INTEGER PRIMARY KEY AUTOINCREMENT,
                رقم_العميل INTEGER,
                نوع_الجهاز TEXT NOT NULL,
                موديل_الجهاز TEXT NOT NULL,
                المشكلة TEXT NOT NULL,
                التكلفة REAL,
                الحالة TEXT DEFAULT 'في الانتظار',
                الفني TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_العميل) REFERENCES العملاء (الرقم)
            )
        ''')

        # جدول المخزون
        self.مؤشر.execute('''
            CREATE TABLE IF NOT EXISTS المخزون (
                رقم_القطعة INTEGER PRIMARY KEY AUTOINCREMENT,
                اسم_القطعة TEXT NOT NULL,
                رقم_الجزء TEXT UNIQUE,
                الكمية INTEGER DEFAULT 0,
                السعر REAL,
                الحد_الأدنى INTEGER DEFAULT 5
            )
        ''')

        # جدول المدفوعات
        self.مؤشر.execute('''
            CREATE TABLE IF NOT EXISTS المدفوعات (
                رقم_الدفعة INTEGER PRIMARY KEY AUTOINCREMENT,
                رقم_الطلب INTEGER,
                المبلغ REAL,
                تاريخ_الدفع TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_الطلب) REFERENCES الطلبات (رقم_الطلب)
            )
        ''')

        self.اتصال.commit()
        self.إنشاء_بيانات_تجريبية()

    def إنشاء_بيانات_تجريبية(self):
        """إنشاء بيانات تجريبية مصرية"""
        # فحص وجود بيانات
        self.مؤشر.execute("SELECT COUNT(*) FROM العملاء")
        if self.مؤشر.fetchone()[0] == 0:
            # عملاء مصريين
            عملاء = [
                ("أحمد محمد علي", "01012345678", "<EMAIL>", "القاهرة - مصر الجديدة"),
                ("فاطمة حسن محمود", "01098765432", "<EMAIL>", "الجيزة - المهندسين"),
                ("محمد عبدالله السيد", "01155555555", "<EMAIL>", "الإسكندرية - سيدي جابر"),
                ("نورا خالد أحمد", "01166666666", "<EMAIL>", "القاهرة - مدينة نصر"),
                ("عمر صلاح الدين", "01177777777", "<EMAIL>", "الجيزة - الدقي"),
            ]

            for عميل in عملاء:
                self.مؤشر.execute("INSERT INTO العملاء (الاسم, الهاتف, الإيميل, العنوان) VALUES (?, ?, ?, ?)", عميل)

            # قطع غيار
            قطع_غيار = [
                ("شاشة آيفون 14 برو", "IP14P-LCD", 10, 2500.00, 2),
                ("بطارية سامسونج جالاكسي S23", "SG23-BAT", 15, 450.00, 3),
                ("كاميرا خلفية آيفون 13", "IP13-CAM", 8, 800.00, 2),
                ("شاحن سريع تايب سي", "CHG-TC-65W", 25, 150.00, 5),
                ("واقي شاشة زجاجي", "GLASS-PROT", 50, 35.00, 10),
                ("سماعة داخلية آيفون", "IP-SPK", 12, 200.00, 3),
            ]

            for قطعة in قطع_غيار:
                self.مؤشر.execute("INSERT INTO المخزون (اسم_القطعة, رقم_الجزء, الكمية, السعر, الحد_الأدنى) VALUES (?, ?, ?, ?, ?)", قطعة)

            # طلبات صيانة
            طلبات = [
                (1, "آيفون", "14 برو", "شاشة مكسورة مع خطوط ملونة", 2500.00, "في الانتظار", "أحمد الفني"),
                (2, "سامسونج", "جالاكسي S23", "بطارية لا تشحن", 450.00, "قيد الإصلاح", "محمد الفني"),
                (3, "آيفون", "13", "كاميرا خلفية لا تعمل", 800.00, "مكتمل", "أحمد الفني"),
                (4, "شاومي", "Mi 11", "مشكلة في الصوت", 300.00, "تم التسليم", "علي الفني"),
            ]

            for طلب in طلبات:
                self.مؤشر.execute("INSERT INTO الطلبات (رقم_العميل, نوع_الجهاز, موديل_الجهاز, المشكلة, التكلفة, الحالة, الفني) VALUES (?, ?, ?, ?, ?, ?, ?)", طلب)

            self.اتصال.commit()

    def إنشاء_الواجهة(self):
        """إنشاء الواجهة الرئيسية بأفضل تصميم مصري"""
        # الحاوي الرئيسي
        self.الحاوي_الرئيسي = tk.Frame(self, bg=self.ألوان['خلفية_رئيسية'])
        self.الحاوي_الرئيسي.pack(fill='both', expand=True, padx=10, pady=10)

        # الشريط العلوي الذهبي
        self.إنشاء_الشريط_العلوي()

        # منطقة البطاقات الإحصائية
        self.إنشاء_بطاقات_الإحصائيات()

        # المنطقة الوسطى - التبويبات
        self.إنشاء_التبويبات()

        # الشريط السفلي
        self.إنشاء_الشريط_السفلي()

    def إنشاء_الشريط_العلوي(self):
        """إنشاء شريط علوي ذهبي أنيق"""
        الشريط_العلوي = tk.Frame(self.الحاوي_الرئيسي, bg=self.ألوان['ذهبي_مصري'], height=80)
        الشريط_العلوي.pack(fill='x', pady=(0, 15))
        الشريط_العلوي.pack_propagate(False)

        # إطار العنوان
        إطار_العنوان = tk.Frame(الشريط_العلوي, bg=self.ألوان['ذهبي_مصري'])
        إطار_العنوان.pack(expand=True, fill='both')

        # العنوان الرئيسي
        العنوان = tk.Label(إطار_العنوان,
                           text="🇪🇬 محل صيانة الجوالات المصري العصري 2025",
                           font=self.خطوط['عنوان_كبير'],
                           bg=self.ألوان['ذهبي_مصري'],
                           fg=self.ألوان['خلفية_رئيسية'])
        العنوان.pack(expand=True)

        # الوقت والتاريخ
        self.تسمية_الوقت = tk.Label(الشريط_العلوي,
                                    text="",
                                    font=self.خطوط['نص_صغير'],
                                    bg=self.ألوان['ذهبي_مصري'],
                                    fg=self.ألوان['خلفية_رئيسية'])
        self.تسمية_الوقت.pack(side='right', padx=20, pady=10)

    def إنشاء_بطاقات_الإحصائيات(self):
        """إنشاء بطاقات إحصائية دائرية أنيقة"""
        إطار_البطاقات = tk.Frame(self.الحاوي_الرئيسي, bg=self.ألوان['خلفية_رئيسية'])
        إطار_البطاقات.pack(fill='x', pady=(0, 20))

        # بيانات البطاقات
        البطاقات = [
            ("👥", "العملاء", "0", self.ألوان['أخضر_نيلي']),
            ("🔧", "الطلبات", "0", self.ألوان['أحمر_مصري']),
            ("📦", "المخزون", "0", self.ألوان['بنفسجي_ملكي']),
            ("💰", "الإيرادات", "0 جنيه", self.ألوان['ذهبي_مصري'])
        ]

        self.بطاقات_الإحصائيات = {}

        for أيقونة, عنوان, قيمة, لون in البطاقات:
            بطاقة = self.إنشاء_بطاقة_دائرية(إطار_البطاقات, أيقونة, عنوان, قيمة, لون)
            بطاقة.pack(side='left', fill='both', expand=True, padx=10)
            self.بطاقات_الإحصائيات[عنوان] = بطاقة

    def إنشاء_بطاقة_دائرية(self, الوالد, أيقونة, عنوان, قيمة, لون):
        """إنشاء بطاقة دائرية أنيقة مع حواف ناعمة"""
        # إطار خارجي للحواف الدائرية مع تأثير الظل
        إطار_خارجي = tk.Frame(الوالد, bg=self.ألوان['خلفية_رئيسية'], highlightthickness=0)

        # البطاقة الرئيسية مع حواف ناعمة
        البطاقة = tk.Frame(إطار_خارجي, bg=لون, relief='raised', bd=2,
                           highlightthickness=0)
        البطاقة.pack(padx=4, pady=4, fill='both', expand=True)

        # إطار داخلي للمحتوى مع مساحة إضافية
        المحتوى = tk.Frame(البطاقة, bg=لون, padx=25, pady=20)
        المحتوى.pack(fill='both', expand=True)

        # الأيقونة مع تأثير بصري
        تسمية_الأيقونة = tk.Label(المحتوى, text=أيقونة,
                                   font=('Arial', 36),
                                   bg=لون, fg=self.ألوان['أبيض_لؤلؤي'])
        تسمية_الأيقونة.pack(pady=(5, 15))

        # القيمة مع خط أكبر وأوضح
        تسمية_القيمة = tk.Label(المحتوى, text=قيمة,
                                 font=('Arial', 20, 'bold'),
                                 bg=لون, fg=self.ألوان['أبيض_لؤلؤي'])
        تسمية_القيمة.pack(pady=(0, 8))

        # العنوان مع تباعد أفضل
        تسمية_العنوان = tk.Label(المحتوى, text=عنوان,
                                  font=('Arial', 12, 'bold'),
                                  bg=لون, fg=self.ألوان['أبيض_لؤلؤي'])
        تسمية_العنوان.pack(pady=(0, 5))

        # تأثيرات تفاعلية للبطاقة
        def عند_دخول_البطاقة(حدث):
            البطاقة.configure(relief='raised', bd=3)

        def عند_خروج_البطاقة(حدث):
            البطاقة.configure(relief='raised', bd=2)

        البطاقة.bind('<Enter>', عند_دخول_البطاقة)
        البطاقة.bind('<Leave>', عند_خروج_البطاقة)
        المحتوى.bind('<Enter>', عند_دخول_البطاقة)
        المحتوى.bind('<Leave>', عند_خروج_البطاقة)

        # حفظ مرجع للقيمة للتحديث
        إطار_خارجي.تسمية_القيمة = تسمية_القيمة

        return إطار_خارجي

    def إنشاء_التبويبات(self):
        """إنشاء نظام التبويبات الأنيق"""
        # إطار التبويبات
        إطار_التبويبات = tk.Frame(self.الحاوي_الرئيسي, bg=self.ألوان['خلفية_رئيسية'])
        إطار_التبويبات.pack(fill='both', expand=True)

        # إنشاء دفتر التبويبات
        self.دفتر_التبويبات = ttk.Notebook(إطار_التبويبات)

        # تخصيص أسلوب التبويبات
        الأسلوب = ttk.Style()
        الأسلوب.theme_use('clam')
        الأسلوب.configure('TNotebook', background=self.ألوان['خلفية_رئيسية'])
        الأسلوب.configure('TNotebook.Tab',
                         background=self.ألوان['خلفية_ثانوية'],
                         foreground=self.ألوان['أبيض_لؤلؤي'],
                         padding=[20, 10],
                         font=self.خطوط['عنوان_صغير'])
        الأسلوب.map('TNotebook.Tab',
                   background=[('selected', self.ألوان['ذهبي_مصري'])],
                   foreground=[('selected', self.ألوان['خلفية_رئيسية'])])

        # إنشاء التبويبات
        self.إنشاء_تبويب_العملاء()
        self.إنشاء_تبويب_الطلبات()
        self.إنشاء_تبويب_المخزون()
        self.إنشاء_تبويب_التقارير()

        self.دفتر_التبويبات.pack(fill='both', expand=True, padx=5, pady=5)

    def إنشاء_تبويب_العملاء(self):
        """إنشاء تبويب إدارة العملاء"""
        # إطار العملاء
        إطار_العملاء = tk.Frame(self.دفتر_التبويبات, bg=self.ألوان['خلفية_ثانوية'])
        self.دفتر_التبويبات.add(إطار_العملاء, text="👥 إدارة العملاء")

        # شريط الأدوات
        شريط_الأدوات = tk.Frame(إطار_العملاء, bg=self.ألوان['خلفية_ثانوية'], height=60)
        شريط_الأدوات.pack(fill='x', padx=10, pady=10)
        شريط_الأدوات.pack_propagate(False)

        # أزرار الأدوات
        self.إنشاء_زر_دائري(شريط_الأدوات, "➕ عميل جديد", self.ألوان['أخضر_نيلي'], self.إضافة_عميل).pack(side='left', padx=5)
        self.إنشاء_زر_دائري(شريط_الأدوات, "✏️ تعديل", self.ألوان['ذهبي_مصري'], self.تعديل_عميل).pack(side='left', padx=5)
        self.إنشاء_زر_دائري(شريط_الأدوات, "🗑️ حذف", self.ألوان['أحمر_مصري'], self.حذف_عميل).pack(side='left', padx=5)

        # حقل البحث
        إطار_البحث = tk.Frame(شريط_الأدوات, bg=self.ألوان['خلفية_ثانوية'])
        إطار_البحث.pack(side='right', padx=10)

        tk.Label(إطار_البحث, text="🔍 بحث:",
                font=self.خطوط['نص_عادي'],
                bg=self.ألوان['خلفية_ثانوية'],
                fg=self.ألوان['أبيض_لؤلؤي']).pack(side='left', padx=5)

        # إطار حقل البحث مع حواف دائرية
        إطار_حقل_البحث = tk.Frame(إطار_البحث, bg=self.ألوان['أبيض_لؤلؤي'],
                                   relief='raised', bd=1, padx=2, pady=2)
        إطار_حقل_البحث.pack(side='left', padx=5)

        self.حقل_بحث_العملاء = tk.Entry(إطار_حقل_البحث, font=('Arial', 11), width=22,
                                        relief='flat', bd=0, bg=self.ألوان['أبيض_لؤلؤي'],
                                        fg=self.ألوان['خلفية_رئيسية'],
                                        insertbackground=self.ألوان['ذهبي_مصري'])
        self.حقل_بحث_العملاء.pack(padx=8, pady=6)
        self.حقل_بحث_العملاء.bind('<KeyRelease>', self.بحث_العملاء)

        # جدول العملاء مع حواف دائرية
        إطار_الجدول_الخارجي = tk.Frame(إطار_العملاء, bg=self.ألوان['خلفية_ثانوية'])
        إطار_الجدول_الخارجي.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        # إطار داخلي للجدول مع حواف ناعمة
        إطار_الجدول = tk.Frame(إطار_الجدول_الخارجي, bg=self.ألوان['أبيض_لؤلؤي'],
                                relief='raised', bd=2, padx=5, pady=5)
        إطار_الجدول.pack(fill='both', expand=True, padx=5, pady=5)

        # إعداد الجدول
        الأعمدة = ("الرقم", "الاسم", "الهاتف", "الإيميل", "العنوان", "تاريخ التسجيل")
        self.جدول_العملاء = ttk.Treeview(إطار_الجدول, columns=الأعمدة, show="headings", height=15)

        # تخصيص أسلوب الجدول المحسن
        الأسلوب = ttk.Style()
        الأسلوب.configure("Treeview",
                         background=self.ألوان['أبيض_لؤلؤي'],
                         foreground=self.ألوان['خلفية_رئيسية'],
                         fieldbackground=self.ألوان['أبيض_لؤلؤي'],
                         font=('Arial', 11),
                         rowheight=30,
                         relief='flat',
                         borderwidth=0)
        الأسلوب.configure("Treeview.Heading",
                         background=self.ألوان['ذهبي_مصري'],
                         foreground=self.ألوان['خلفية_رئيسية'],
                         font=('Arial', 12, 'bold'),
                         relief='raised',
                         borderwidth=1)

        # تخصيص شريط التمرير
        الأسلوب.configure("Vertical.TScrollbar",
                         background=self.ألوان['خلفية_ثانوية'],
                         troughcolor=self.ألوان['أبيض_لؤلؤي'],
                         arrowcolor=self.ألوان['ذهبي_مصري'],
                         borderwidth=0,
                         relief='flat')

        # إعداد عناوين الأعمدة
        for عمود in الأعمدة:
            self.جدول_العملاء.heading(عمود, text=عمود)
            if عمود == "الرقم":
                self.جدول_العملاء.column(عمود, width=80, anchor='center')
            elif عمود == "الهاتف":
                self.جدول_العملاء.column(عمود, width=120, anchor='center')
            else:
                self.جدول_العملاء.column(عمود, width=150)

        # شريط التمرير المحسن
        شريط_التمرير = ttk.Scrollbar(إطار_الجدول, orient="vertical", command=self.جدول_العملاء.yview)
        self.جدول_العملاء.configure(yscrollcommand=شريط_التمرير.set)

        self.جدول_العملاء.pack(side="left", fill="both", expand=True, padx=2, pady=2)
        شريط_التمرير.pack(side="right", fill="y", padx=(2, 0))

        # ربط الأحداث
        self.جدول_العملاء.bind('<Double-1>', self.عرض_تفاصيل_العميل)

    def إنشاء_زر_دائري(self, الوالد, النص, اللون, الأمر):
        """إنشاء زر بحواف دائرية ناعمة وأنيقة"""
        # إطار خارجي للحواف الدائرية
        إطار_الزر = tk.Frame(الوالد, bg=الوالد.cget('bg'), highlightthickness=0)

        الزر = tk.Button(إطار_الزر, text=النص, command=الأمر,
                         font=self.خطوط['عنوان_صغير'],
                         bg=اللون, fg=self.ألوان['أبيض_لؤلؤي'],
                         relief='flat', bd=0,
                         padx=25, pady=12,
                         cursor='hand2',
                         activebackground=self.تفتيح_اللون(اللون),
                         activeforeground=self.ألوان['أبيض_لؤلؤي'],
                         highlightthickness=0,
                         borderwidth=0)

        الزر.pack(padx=3, pady=3, fill='both', expand=True)

        # تأثيرات التفاعل المحسنة
        def عند_الدخول(حدث):
            الزر.configure(bg=self.تفتيح_اللون(اللون))
            # تأثير رفع الزر
            الزر.configure(relief='raised', bd=1)

        def عند_الخروج(حدث):
            الزر.configure(bg=اللون)
            الزر.configure(relief='flat', bd=0)

        def عند_الضغط(حدث):
            الزر.configure(relief='sunken', bd=2)

        def عند_الإفلات(حدث):
            الزر.configure(relief='raised', bd=1)

        الزر.bind('<Enter>', عند_الدخول)
        الزر.bind('<Leave>', عند_الخروج)
        الزر.bind('<Button-1>', عند_الضغط)
        الزر.bind('<ButtonRelease-1>', عند_الإفلات)

        return إطار_الزر

    def تفتيح_اللون(self, لون):
        """تفتيح اللون للتأثيرات"""
        ألوان_مفتحة = {
            self.ألوان['أخضر_نيلي']: '#3cb371',
            self.ألوان['أحمر_مصري']: '#dc143c',
            self.ألوان['ذهبي_مصري']: '#ffd700',
            self.ألوان['بنفسجي_ملكي']: '#8a2be2'
        }
        return ألوان_مفتحة.get(لون, لون)

    def إنشاء_تبويب_الطلبات(self):
        """إنشاء تبويب إدارة الطلبات"""
        إطار_الطلبات = tk.Frame(self.دفتر_التبويبات, bg=self.ألوان['خلفية_ثانوية'])
        self.دفتر_التبويبات.add(إطار_الطلبات, text="🔧 طلبات الصيانة")

        # شريط الأدوات
        شريط_الأدوات = tk.Frame(إطار_الطلبات, bg=self.ألوان['خلفية_ثانوية'], height=60)
        شريط_الأدوات.pack(fill='x', padx=10, pady=10)
        شريط_الأدوات.pack_propagate(False)

        self.إنشاء_زر_دائري(شريط_الأدوات, "➕ طلب جديد", self.ألوان['أخضر_نيلي'], self.إضافة_طلب).pack(side='left', padx=5)
        self.إنشاء_زر_دائري(شريط_الأدوات, "🔄 تحديث الحالة", self.ألوان['ذهبي_مصري'], self.تحديث_حالة_الطلب).pack(side='left', padx=5)
        self.إنشاء_زر_دائري(شريط_الأدوات, "🖨️ طباعة", self.ألوان['بنفسجي_ملكي'], self.طباعة_الطلب).pack(side='left', padx=5)

        # جدول الطلبات
        إطار_الجدول = tk.Frame(إطار_الطلبات, bg=self.ألوان['خلفية_ثانوية'])
        إطار_الجدول.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        الأعمدة = ("رقم الطلب", "العميل", "نوع الجهاز", "الموديل", "المشكلة", "التكلفة", "الحالة", "الفني")
        self.جدول_الطلبات = ttk.Treeview(إطار_الجدول, columns=الأعمدة, show="headings", height=15)

        for عمود in الأعمدة:
            self.جدول_الطلبات.heading(عمود, text=عمود)
            self.جدول_الطلبات.column(عمود, width=120)

        شريط_التمرير2 = ttk.Scrollbar(إطار_الجدول, orient="vertical", command=self.جدول_الطلبات.yview)
        self.جدول_الطلبات.configure(yscrollcommand=شريط_التمرير2.set)

        self.جدول_الطلبات.pack(side="left", fill="both", expand=True)
        شريط_التمرير2.pack(side="right", fill="y")

    def إنشاء_تبويب_المخزون(self):
        """إنشاء تبويب إدارة المخزون"""
        إطار_المخزون = tk.Frame(self.دفتر_التبويبات, bg=self.ألوان['خلفية_ثانوية'])
        self.دفتر_التبويبات.add(إطار_المخزون, text="📦 إدارة المخزون")

        # شريط الأدوات
        شريط_الأدوات = tk.Frame(إطار_المخزون, bg=self.ألوان['خلفية_ثانوية'], height=60)
        شريط_الأدوات.pack(fill='x', padx=10, pady=10)
        شريط_الأدوات.pack_propagate(False)

        self.إنشاء_زر_دائري(شريط_الأدوات, "➕ قطعة جديدة", self.ألوان['أخضر_نيلي'], self.إضافة_قطعة).pack(side='left', padx=5)
        self.إنشاء_زر_دائري(شريط_الأدوات, "📈 تحديث الكمية", self.ألوان['ذهبي_مصري'], self.تحديث_كمية).pack(side='left', padx=5)
        self.إنشاء_زر_دائري(شريط_الأدوات, "⚠️ تنبيهات", self.ألوان['أحمر_مصري'], self.عرض_تنبيهات_المخزون).pack(side='left', padx=5)

        # جدول المخزون
        إطار_الجدول = tk.Frame(إطار_المخزون, bg=self.ألوان['خلفية_ثانوية'])
        إطار_الجدول.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        الأعمدة = ("رقم القطعة", "اسم القطعة", "رقم الجزء", "الكمية", "السعر", "الحد الأدنى", "الحالة")
        self.جدول_المخزون = ttk.Treeview(إطار_الجدول, columns=الأعمدة, show="headings", height=15)

        for عمود in الأعمدة:
            self.جدول_المخزون.heading(عمود, text=عمود)
            self.جدول_المخزون.column(عمود, width=120)

        شريط_التمرير3 = ttk.Scrollbar(إطار_الجدول, orient="vertical", command=self.جدول_المخزون.yview)
        self.جدول_المخزون.configure(yscrollcommand=شريط_التمرير3.set)

        self.جدول_المخزون.pack(side="left", fill="both", expand=True)
        شريط_التمرير3.pack(side="right", fill="y")

    def إنشاء_تبويب_التقارير(self):
        """إنشاء تبويب التقارير والإحصائيات"""
        إطار_التقارير = tk.Frame(self.دفتر_التبويبات, bg=self.ألوان['خلفية_ثانوية'])
        self.دفتر_التبويبات.add(إطار_التقارير, text="📊 التقارير والإحصائيات")

        # عنوان التقارير
        عنوان = tk.Label(إطار_التقارير, text="📊 التقارير والإحصائيات المالية",
                         font=self.خطوط['عنوان_متوسط'],
                         bg=self.ألوان['خلفية_ثانوية'],
                         fg=self.ألوان['ذهبي_مصري'])
        عنوان.pack(pady=20)

        # إطار التقارير
        إطار_أزرار_التقارير = tk.Frame(إطار_التقارير, bg=self.ألوان['خلفية_ثانوية'])
        إطار_أزرار_التقارير.pack(pady=20)

        # أزرار التقارير
        تقارير = [
            ("📈 تقرير الإيرادات", self.ألوان['أخضر_نيلي'], self.تقرير_الإيرادات),
            ("👥 تقرير العملاء", self.ألوان['بنفسجي_ملكي'], self.تقرير_العملاء),
            ("🔧 تقرير الطلبات", self.ألوان['أحمر_مصري'], self.تقرير_الطلبات),
            ("📦 تقرير المخزون", self.ألوان['ذهبي_مصري'], self.تقرير_المخزون)
        ]

        for نص, لون, أمر in تقارير:
            self.إنشاء_زر_دائري(إطار_أزرار_التقارير, نص, لون, أمر).pack(pady=10, padx=20, fill='x')

    def إنشاء_الشريط_السفلي(self):
        """إنشاء شريط سفلي أنيق"""
        الشريط_السفلي = tk.Frame(self.الحاوي_الرئيسي, bg=self.ألوان['خلفية_ثانوية'], height=40)
        الشريط_السفلي.pack(fill='x', side='bottom')
        الشريط_السفلي.pack_propagate(False)

        # حالة النظام
        حالة_النظام = tk.Label(الشريط_السفلي, text="🟢 النظام يعمل بشكل طبيعي",
                               font=self.خطوط['نص_صغير'],
                               bg=self.ألوان['خلفية_ثانوية'],
                               fg=self.ألوان['أخضر_نيلي'])
        حالة_النظام.pack(side='left', padx=20, pady=10)

        # معلومات النسخة
        معلومات_النسخة = tk.Label(الشريط_السفلي, text="🇪🇬 النسخة المصرية العصرية 2025 v1.0",
                                  font=self.خطوط['نص_صغير'],
                                  bg=self.ألوان['خلفية_ثانوية'],
                                  fg=self.ألوان['ذهبي_مصري'])
        معلومات_النسخة.pack(side='right', padx=20, pady=10)

    def تحميل_البيانات(self):
        """تحميل وتحديث جميع البيانات"""
        self.تحميل_العملاء()
        self.تحميل_الطلبات()
        self.تحميل_المخزون()
        self.تحديث_الإحصائيات()

    def تحميل_العملاء(self):
        """تحميل بيانات العملاء"""
        # مسح البيانات الحالية
        for عنصر in self.جدول_العملاء.get_children():
            self.جدول_العملاء.delete(عنصر)

        # تحميل البيانات الجديدة
        self.مؤشر.execute("SELECT * FROM العملاء ORDER BY الرقم DESC")
        العملاء = self.مؤشر.fetchall()

        for عميل in العملاء:
            self.جدول_العملاء.insert('', 'end', values=عميل)

    def تحميل_الطلبات(self):
        """تحميل بيانات الطلبات"""
        for عنصر in self.جدول_الطلبات.get_children():
            self.جدول_الطلبات.delete(عنصر)

        self.مؤشر.execute("""
            SELECT ط.رقم_الطلب, ع.الاسم, ط.نوع_الجهاز, ط.موديل_الجهاز,
                   ط.المشكلة, ط.التكلفة, ط.الحالة, ط.الفني
            FROM الطلبات ط
            JOIN العملاء ع ON ط.رقم_العميل = ع.الرقم
            ORDER BY ط.رقم_الطلب DESC
        """)
        الطلبات = self.مؤشر.fetchall()

        for طلب in الطلبات:
            # تلوين الصفوف حسب الحالة
            if طلب[6] == "مكتمل":
                tags = ('مكتمل',)
            elif طلب[6] == "قيد الإصلاح":
                tags = ('قيد_الإصلاح',)
            else:
                tags = ('عادي',)

            self.جدول_الطلبات.insert('', 'end', values=طلب, tags=tags)

        # تخصيص ألوان الصفوف
        self.جدول_الطلبات.tag_configure('مكتمل', background='#90EE90')
        self.جدول_الطلبات.tag_configure('قيد_الإصلاح', background='#FFE4B5')

    def تحميل_المخزون(self):
        """تحميل بيانات المخزون"""
        for عنصر in self.جدول_المخزون.get_children():
            self.جدول_المخزون.delete(عنصر)

        self.مؤشر.execute("SELECT * FROM المخزون ORDER BY رقم_القطعة")
        المخزون = self.مؤشر.fetchall()

        for قطعة in المخزون:
            # تحديد حالة المخزون
            if قطعة[3] <= قطعة[5]:  # الكمية <= الحد الأدنى
                حالة = "⚠️ منخفض"
                tags = ('منخفض',)
            elif قطعة[3] == 0:
                حالة = "❌ نفد"
                tags = ('نفد',)
            else:
                حالة = "✅ متوفر"
                tags = ('متوفر',)

            قيم = list(قطعة) + [حالة]
            self.جدول_المخزون.insert('', 'end', values=قيم, tags=tags)

        # تخصيص ألوان المخزون
        self.جدول_المخزون.tag_configure('منخفض', background='#FFE4B5')
        self.جدول_المخزون.tag_configure('نفد', background='#FFB6C1')
        self.جدول_المخزون.tag_configure('متوفر', background='#90EE90')

    def تحديث_الإحصائيات(self):
        """تحديث بطاقات الإحصائيات"""
        try:
            # عدد العملاء
            self.مؤشر.execute("SELECT COUNT(*) FROM العملاء")
            عدد_العملاء = self.مؤشر.fetchone()[0]

            # عدد الطلبات
            self.مؤشر.execute("SELECT COUNT(*) FROM الطلبات")
            عدد_الطلبات = self.مؤشر.fetchone()[0]

            # عدد قطع المخزون
            self.مؤشر.execute("SELECT COUNT(*) FROM المخزون")
            عدد_المخزون = self.مؤشر.fetchone()[0]

            # إجمالي الإيرادات
            self.مؤشر.execute("SELECT SUM(المبلغ) FROM المدفوعات")
            الإيرادات = self.مؤشر.fetchone()[0] or 0

            # تحديث البطاقات
            if "العملاء" in self.بطاقات_الإحصائيات:
                self.بطاقات_الإحصائيات["العملاء"].تسمية_القيمة.config(text=str(عدد_العملاء))

            if "الطلبات" in self.بطاقات_الإحصائيات:
                self.بطاقات_الإحصائيات["الطلبات"].تسمية_القيمة.config(text=str(عدد_الطلبات))

            if "المخزون" in self.بطاقات_الإحصائيات:
                self.بطاقات_الإحصائيات["المخزون"].تسمية_القيمة.config(text=str(عدد_المخزون))

            if "الإيرادات" in self.بطاقات_الإحصائيات:
                self.بطاقات_الإحصائيات["الإيرادات"].تسمية_القيمة.config(text=f"{الإيرادات:.0f} جنيه")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def تحديث_الوقت(self):
        """تحديث الوقت والتاريخ"""
        الآن = datetime.now()
        الوقت = الآن.strftime("%H:%M:%S")
        التاريخ = الآن.strftime("%Y-%m-%d")
        النص = f"📅 {التاريخ} ⏰ {الوقت}"
        self.تسمية_الوقت.config(text=النص)
        self.after(1000, self.تحديث_الوقت)

    # وظائف العملاء
    def إضافة_عميل(self):
        """إضافة عميل جديد"""
        نافذة = self.إنشاء_نافذة_حوار("➕ إضافة عميل جديد")

        # الحقول
        حقول = [
            ("الاسم الكامل:", "اسم"),
            ("رقم الهاتف:", "هاتف"),
            ("البريد الإلكتروني:", "إيميل"),
            ("العنوان:", "عنوان")
        ]

        متغيرات = {}
        for تسمية, مفتاح in حقول:
            إطار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
            إطار.pack(fill='x', padx=20, pady=10)

            tk.Label(إطار, text=تسمية, font=self.خطوط['نص_عادي'],
                    bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['خلفية_رئيسية']).pack(anchor='w')

            if مفتاح == "عنوان":
                حقل = tk.Text(إطار, height=3, font=self.خطوط['نص_عادي'])
                متغيرات[مفتاح] = حقل
            else:
                متغير = tk.StringVar()
                حقل = tk.Entry(إطار, textvariable=متغير, font=self.خطوط['نص_عادي'])
                متغيرات[مفتاح] = متغير

            حقل.pack(fill='x', pady=5)

        # أزرار
        إطار_الأزرار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
        إطار_الأزرار.pack(fill='x', padx=20, pady=20)

        def حفظ():
            try:
                اسم = متغيرات["اسم"].get().strip()
                هاتف = متغيرات["هاتف"].get().strip()
                إيميل = متغيرات["إيميل"].get().strip()

                if isinstance(متغيرات["عنوان"], tk.Text):
                    عنوان = متغيرات["عنوان"].get('1.0', 'end-1c').strip()
                else:
                    عنوان = متغيرات["عنوان"].get().strip()

                if not اسم or not هاتف:
                    messagebox.showerror("خطأ", "يرجى إدخال الاسم ورقم الهاتف")
                    return

                # التحقق من رقم الهاتف المصري
                if not re.match(r'^01[0-9]{9}$', هاتف):
                    messagebox.showerror("خطأ", "رقم الهاتف يجب أن يكون مصري (01xxxxxxxxx)")
                    return

                self.مؤشر.execute("INSERT INTO العملاء (الاسم, الهاتف, الإيميل, العنوان) VALUES (?, ?, ?, ?)",
                                (اسم, هاتف, إيميل, عنوان))
                self.اتصال.commit()

                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                نافذة.destroy()
                self.تحميل_العملاء()
                self.تحديث_الإحصائيات()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

        self.إنشاء_زر_دائري(إطار_الأزرار, "💾 حفظ", self.ألوان['أخضر_نيلي'], حفظ).pack(side='left', padx=10)
        self.إنشاء_زر_دائري(إطار_الأزرار, "❌ إلغاء", self.ألوان['أحمر_مصري'], نافذة.destroy).pack(side='left', padx=10)

    def تعديل_عميل(self):
        """تعديل عميل محدد"""
        المحدد = self.جدول_العملاء.selection()
        if not المحدد:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للتعديل")
            return

        القيم = self.جدول_العملاء.item(المحدد[0], 'values')
        رقم_العميل = القيم[0]

        نافذة = self.إنشاء_نافذة_حوار("✏️ تعديل بيانات العميل")

        # الحقول مع القيم الحالية
        حقول = [
            ("الاسم الكامل:", "اسم", القيم[1]),
            ("رقم الهاتف:", "هاتف", القيم[2]),
            ("البريد الإلكتروني:", "إيميل", القيم[3]),
            ("العنوان:", "عنوان", القيم[4])
        ]

        متغيرات = {}
        for تسمية, مفتاح, قيمة in حقول:
            إطار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
            إطار.pack(fill='x', padx=20, pady=10)

            tk.Label(إطار, text=تسمية, font=self.خطوط['نص_عادي'],
                    bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['خلفية_رئيسية']).pack(anchor='w')

            if مفتاح == "عنوان":
                حقل = tk.Text(إطار, height=3, font=self.خطوط['نص_عادي'])
                حقل.insert('1.0', قيمة or "")
                متغيرات[مفتاح] = حقل
            else:
                متغير = tk.StringVar(value=قيمة or "")
                حقل = tk.Entry(إطار, textvariable=متغير, font=self.خطوط['نص_عادي'])
                متغيرات[مفتاح] = متغير

            حقل.pack(fill='x', pady=5)

        # أزرار
        إطار_الأزرار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
        إطار_الأزرار.pack(fill='x', padx=20, pady=20)

        def تحديث():
            try:
                اسم = متغيرات["اسم"].get().strip()
                هاتف = متغيرات["هاتف"].get().strip()
                إيميل = متغيرات["إيميل"].get().strip()

                if isinstance(متغيرات["عنوان"], tk.Text):
                    عنوان = متغيرات["عنوان"].get('1.0', 'end-1c').strip()
                else:
                    عنوان = متغيرات["عنوان"].get().strip()

                if not اسم or not هاتف:
                    messagebox.showerror("خطأ", "يرجى إدخال الاسم ورقم الهاتف")
                    return

                self.مؤشر.execute("UPDATE العملاء SET الاسم=?, الهاتف=?, الإيميل=?, العنوان=? WHERE الرقم=?",
                                (اسم, هاتف, إيميل, عنوان, رقم_العميل))
                self.اتصال.commit()

                messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                نافذة.destroy()
                self.تحميل_العملاء()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

        self.إنشاء_زر_دائري(إطار_الأزرار, "💾 تحديث", self.ألوان['ذهبي_مصري'], تحديث).pack(side='left', padx=10)
        self.إنشاء_زر_دائري(إطار_الأزرار, "❌ إلغاء", self.ألوان['أحمر_مصري'], نافذة.destroy).pack(side='left', padx=10)

    def حذف_عميل(self):
        """حذف عميل محدد"""
        المحدد = self.جدول_العملاء.selection()
        if not المحدد:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للحذف")
            return

        القيم = self.جدول_العملاء.item(المحدد[0], 'values')
        اسم_العميل = القيم[1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف العميل '{اسم_العميل}'؟\nسيتم حذف جميع طلباته أيضاً"):
            try:
                رقم_العميل = القيم[0]
                self.مؤشر.execute("DELETE FROM الطلبات WHERE رقم_العميل=?", (رقم_العميل,))
                self.مؤشر.execute("DELETE FROM العملاء WHERE الرقم=?", (رقم_العميل,))
                self.اتصال.commit()

                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.تحميل_العملاء()
                self.تحميل_الطلبات()
                self.تحديث_الإحصائيات()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def عرض_تفاصيل_العميل(self, حدث=None):
        """عرض تفاصيل العميل"""
        المحدد = self.جدول_العملاء.selection()
        if not المحدد:
            return

        القيم = self.جدول_العملاء.item(المحدد[0], 'values')
        رقم_العميل = القيم[0]

        نافذة = self.إنشاء_نافذة_حوار(f"👤 تفاصيل العميل: {القيم[1]}")

        # معلومات العميل
        معلومات = [
            ("الرقم:", القيم[0]),
            ("الاسم:", القيم[1]),
            ("الهاتف:", القيم[2]),
            ("الإيميل:", القيم[3]),
            ("العنوان:", القيم[4]),
            ("تاريخ التسجيل:", القيم[5])
        ]

        for تسمية, قيمة in معلومات:
            إطار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
            إطار.pack(fill='x', padx=20, pady=5)

            tk.Label(إطار, text=تسمية, font=self.خطوط['عنوان_صغير'],
                    bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['ذهبي_مصري']).pack(side='left')

            tk.Label(إطار, text=str(قيمة or "غير محدد"), font=self.خطوط['نص_عادي'],
                    bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['خلفية_رئيسية']).pack(side='left', padx=10)

        # طلبات العميل
        tk.Label(نافذة, text="📋 طلبات العميل:", font=self.خطوط['عنوان_صغير'],
                bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['ذهبي_مصري']).pack(pady=20)

        self.مؤشر.execute("SELECT * FROM الطلبات WHERE رقم_العميل=?", (رقم_العميل,))
        طلبات = self.مؤشر.fetchall()

        if طلبات:
            for طلب in طلبات:
                إطار_طلب = tk.Frame(نافذة, bg=self.ألوان['خلفية_ثانوية'], relief='solid', bd=1)
                إطار_طلب.pack(fill='x', padx=20, pady=5)

                tk.Label(إطار_طلب, text=f"🔧 {طلب[2]} {طلب[3]} - {طلب[4]}",
                        font=self.خطوط['نص_عادي'],
                        bg=self.ألوان['خلفية_ثانوية'], fg=self.ألوان['أبيض_لؤلؤي']).pack(padx=10, pady=5)
        else:
            tk.Label(نافذة, text="لا توجد طلبات لهذا العميل",
                    font=self.خطوط['نص_عادي'],
                    bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['رمادي_فضي']).pack(pady=10)

    def بحث_العملاء(self, حدث=None):
        """البحث في العملاء"""
        نص_البحث = self.حقل_بحث_العملاء.get().strip()

        # مسح البيانات الحالية
        for عنصر in self.جدول_العملاء.get_children():
            self.جدول_العملاء.delete(عنصر)

        if نص_البحث:
            self.مؤشر.execute("""
                SELECT * FROM العملاء
                WHERE الاسم LIKE ? OR الهاتف LIKE ? OR الإيميل LIKE ?
                ORDER BY الرقم DESC
            """, (f"%{نص_البحث}%", f"%{نص_البحث}%", f"%{نص_البحث}%"))
        else:
            self.مؤشر.execute("SELECT * FROM العملاء ORDER BY الرقم DESC")

        العملاء = self.مؤشر.fetchall()
        for عميل in العملاء:
            self.جدول_العملاء.insert('', 'end', values=عميل)

    # وظائف الطلبات
    def إضافة_طلب(self):
        """إضافة طلب صيانة جديد"""
        نافذة = self.إنشاء_نافذة_حوار("➕ إضافة طلب صيانة جديد")

        # اختيار العميل
        إطار_العميل = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
        إطار_العميل.pack(fill='x', padx=20, pady=10)

        tk.Label(إطار_العميل, text="العميل:", font=self.خطوط['نص_عادي'],
                bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['خلفية_رئيسية']).pack(anchor='w')

        # جلب العملاء
        self.مؤشر.execute("SELECT الرقم, الاسم FROM العملاء ORDER BY الاسم")
        العملاء = self.مؤشر.fetchall()

        متغير_العميل = tk.StringVar()
        قائمة_العملاء = ttk.Combobox(إطار_العميل, textvariable=متغير_العميل,
                                      values=[f"{عميل[0]} - {عميل[1]}" for عميل in العملاء],
                                      font=self.خطوط['نص_عادي'], state="readonly")
        قائمة_العملاء.pack(fill='x', pady=5)

        # باقي الحقول
        حقول = [
            ("نوع الجهاز:", "نوع_الجهاز"),
            ("موديل الجهاز:", "موديل_الجهاز"),
            ("المشكلة:", "المشكلة"),
            ("التكلفة المقدرة:", "التكلفة"),
            ("الفني المسؤول:", "الفني")
        ]

        متغيرات = {"العميل": متغير_العميل}

        for تسمية, مفتاح in حقول:
            إطار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
            إطار.pack(fill='x', padx=20, pady=10)

            tk.Label(إطار, text=تسمية, font=self.خطوط['نص_عادي'],
                    bg=self.ألوان['أبيض_لؤلؤي'], fg=self.ألوان['خلفية_رئيسية']).pack(anchor='w')

            if مفتاح == "المشكلة":
                حقل = tk.Text(إطار, height=3, font=self.خطوط['نص_عادي'])
                متغيرات[مفتاح] = حقل
            else:
                متغير = tk.StringVar()
                حقل = tk.Entry(إطار, textvariable=متغير, font=self.خطوط['نص_عادي'])
                متغيرات[مفتاح] = متغير

            حقل.pack(fill='x', pady=5)

        # أزرار
        إطار_الأزرار = tk.Frame(نافذة, bg=self.ألوان['أبيض_لؤلؤي'])
        إطار_الأزرار.pack(fill='x', padx=20, pady=20)

        def حفظ_الطلب():
            try:
                عميل_محدد = متغيرات["العميل"].get()
                if not عميل_محدد:
                    messagebox.showerror("خطأ", "يرجى اختيار العميل")
                    return

                رقم_العميل = int(عميل_محدد.split(" - ")[0])
                نوع_الجهاز = متغيرات["نوع_الجهاز"].get().strip()
                موديل_الجهاز = متغيرات["موديل_الجهاز"].get().strip()

                if isinstance(متغيرات["المشكلة"], tk.Text):
                    المشكلة = متغيرات["المشكلة"].get('1.0', 'end-1c').strip()
                else:
                    المشكلة = متغيرات["المشكلة"].get().strip()

                التكلفة = متغيرات["التكلفة"].get().strip()
                الفني = متغيرات["الفني"].get().strip()

                if not all([نوع_الجهاز, موديل_الجهاز, المشكلة]):
                    messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                    return

                try:
                    التكلفة = float(التكلفة) if التكلفة else 0.0
                except ValueError:
                    messagebox.showerror("خطأ", "التكلفة يجب أن تكون رقم")
                    return

                self.مؤشر.execute("""
                    INSERT INTO الطلبات (رقم_العميل, نوع_الجهاز, موديل_الجهاز, المشكلة, التكلفة, الفني)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (رقم_العميل, نوع_الجهاز, موديل_الجهاز, المشكلة, التكلفة, الفني))

                self.اتصال.commit()

                messagebox.showinfo("نجح", "تم إضافة طلب الصيانة بنجاح")
                نافذة.destroy()
                self.تحميل_الطلبات()
                self.تحديث_الإحصائيات()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

        self.إنشاء_زر_دائري(إطار_الأزرار, "💾 حفظ", self.ألوان['أخضر_نيلي'], حفظ_الطلب).pack(side='left', padx=10)
        self.إنشاء_زر_دائري(إطار_الأزرار, "❌ إلغاء", self.ألوان['أحمر_مصري'], نافذة.destroy).pack(side='left', padx=10)

    # وظائف مساعدة
    def إنشاء_نافذة_حوار(self, العنوان):
        """إنشاء نافذة حوار أنيقة مع حواف دائرية ناعمة"""
        نافذة = tk.Toplevel(self)
        نافذة.title(العنوان)
        نافذة.geometry("650x550")
        نافذة.configure(bg=self.ألوان['خلفية_رئيسية'])
        نافذة.resizable(False, False)
        نافذة.transient(self)
        نافذة.grab_set()

        # توسيط النافذة
        نافذة.update_idletasks()
        x = (نافذة.winfo_screenwidth() // 2) - 325
        y = (نافذة.winfo_screenheight() // 2) - 275
        نافذة.geometry(f'650x550+{x}+{y}')

        # إطار خارجي للحواف الدائرية
        إطار_خارجي = tk.Frame(نافذة, bg=self.ألوان['خلفية_رئيسية'], padx=8, pady=8)
        إطار_خارجي.pack(fill='both', expand=True)

        # الإطار الرئيسي مع حواف ناعمة
        إطار_رئيسي = tk.Frame(إطار_خارجي, bg=self.ألوان['أبيض_لؤلؤي'],
                               relief='raised', bd=3, highlightthickness=0)
        إطار_رئيسي.pack(fill='both', expand=True)

        # شريط العنوان مع حواف دائرية
        شريط_العنوان = tk.Frame(إطار_رئيسي, bg=self.ألوان['ذهبي_مصري'],
                                 height=70, relief='flat', bd=0)
        شريط_العنوان.pack(fill='x')
        شريط_العنوان.pack_propagate(False)

        # العنوان مع تأثير بصري
        تسمية_العنوان = tk.Label(شريط_العنوان, text=العنوان,
                                  font=('Arial', 16, 'bold'),
                                  bg=self.ألوان['ذهبي_مصري'],
                                  fg=self.ألوان['خلفية_رئيسية'])
        تسمية_العنوان.pack(expand=True)

        # إضافة تأثير ظل للنافذة
        نافذة.attributes('-alpha', 0.98)

        return إطار_رئيسي

    # وظائف أخرى مبسطة
    def تحديث_حالة_الطلب(self):
        """تحديث حالة طلب الصيانة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def طباعة_الطلب(self):
        """طباعة طلب الصيانة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def إضافة_قطعة(self):
        """إضافة قطعة غيار جديدة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def تحديث_كمية(self):
        """تحديث كمية قطعة غيار"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

    def عرض_تنبيهات_المخزون(self):
        """عرض تنبيهات المخزون المنخفض"""
        self.مؤشر.execute("SELECT * FROM المخزون WHERE الكمية <= الحد_الأدنى")
        قطع_منخفضة = self.مؤشر.fetchall()

        if قطع_منخفضة:
            رسالة = "⚠️ تنبيهات المخزون المنخفض:\n\n"
            for قطعة in قطع_منخفضة:
                رسالة += f"• {قطعة[1]} - الكمية: {قطعة[3]} (الحد الأدنى: {قطعة[5]})\n"
            messagebox.showwarning("تنبيهات المخزون", رسالة)
        else:
            messagebox.showinfo("المخزون", "جميع قطع الغيار متوفرة بكميات كافية")

    def تقرير_الإيرادات(self):
        """تقرير الإيرادات"""
        self.مؤشر.execute("SELECT SUM(المبلغ) FROM المدفوعات")
        إجمالي_الإيرادات = self.مؤشر.fetchone()[0] or 0

        self.مؤشر.execute("SELECT COUNT(*) FROM المدفوعات")
        عدد_المدفوعات = self.مؤشر.fetchone()[0]

        رسالة = f"📈 تقرير الإيرادات:\n\n"
        رسالة += f"💰 إجمالي الإيرادات: {إجمالي_الإيرادات:.2f} جنيه\n"
        رسالة += f"🧾 عدد المدفوعات: {عدد_المدفوعات}\n"
        رسالة += f"📊 متوسط الدفعة: {إجمالي_الإيرادات/عدد_المدفوعات:.2f} جنيه" if عدد_المدفوعات > 0 else "📊 متوسط الدفعة: 0 جنيه"

        messagebox.showinfo("تقرير الإيرادات", رسالة)

    def تقرير_العملاء(self):
        """تقرير العملاء"""
        self.مؤشر.execute("SELECT COUNT(*) FROM العملاء")
        عدد_العملاء = self.مؤشر.fetchone()[0]

        self.مؤشر.execute("""
            SELECT ع.الاسم, COUNT(ط.رقم_الطلب) as عدد_الطلبات
            FROM العملاء ع
            LEFT JOIN الطلبات ط ON ع.الرقم = ط.رقم_العميل
            GROUP BY ع.الرقم
            ORDER BY عدد_الطلبات DESC
            LIMIT 1
        """)
        أفضل_عميل = self.مؤشر.fetchone()

        رسالة = f"👥 تقرير العملاء:\n\n"
        رسالة += f"📊 إجمالي العملاء: {عدد_العملاء}\n"
        if أفضل_عميل:
            رسالة += f"⭐ أفضل عميل: {أفضل_عميل[0]} ({أفضل_عميل[1]} طلبات)"

        messagebox.showinfo("تقرير العملاء", رسالة)

    def تقرير_الطلبات(self):
        """تقرير الطلبات"""
        self.مؤشر.execute("SELECT COUNT(*) FROM الطلبات")
        إجمالي_الطلبات = self.مؤشر.fetchone()[0]

        self.مؤشر.execute("SELECT الحالة, COUNT(*) FROM الطلبات GROUP BY الحالة")
        حالات_الطلبات = self.مؤشر.fetchall()

        رسالة = f"🔧 تقرير الطلبات:\n\n"
        رسالة += f"📊 إجمالي الطلبات: {إجمالي_الطلبات}\n\n"
        رسالة += "📋 توزيع الحالات:\n"
        for حالة, عدد in حالات_الطلبات:
            رسالة += f"• {حالة}: {عدد} طلب\n"

        messagebox.showinfo("تقرير الطلبات", رسالة)

    def تقرير_المخزون(self):
        """تقرير المخزون"""
        self.مؤشر.execute("SELECT COUNT(*) FROM المخزون")
        عدد_القطع = self.مؤشر.fetchone()[0]

        self.مؤشر.execute("SELECT SUM(الكمية * السعر) FROM المخزون")
        قيمة_المخزون = self.مؤشر.fetchone()[0] or 0

        self.مؤشر.execute("SELECT COUNT(*) FROM المخزون WHERE الكمية <= الحد_الأدنى")
        قطع_منخفضة = self.مؤشر.fetchone()[0]

        رسالة = f"📦 تقرير المخزون:\n\n"
        رسالة += f"📊 عدد القطع: {عدد_القطع}\n"
        رسالة += f"💰 قيمة المخزون: {قيمة_المخزون:.2f} جنيه\n"
        رسالة += f"⚠️ قطع منخفضة: {قطع_منخفضة}"

        messagebox.showinfo("تقرير المخزون", رسالة)

    def __del__(self):
        """إغلاق الاتصال عند إنهاء التطبيق"""
        try:
            if hasattr(self, 'اتصال'):
                self.اتصال.close()
        except:
            pass

# تشغيل التطبيق
def main():
    """تشغيل التطبيق المصري العصري"""
    print("🇪🇬 " + "=" * 60)
    print("🇪🇬 تطبيق إدارة محل صيانة الجوالات المصري العصري 2025")
    print("🇪🇬 Egyptian Phone Repair Shop Management System 2025")
    print("🇪🇬 " + "=" * 60)
    print("🎨 تشغيل التطبيق بأفضل تصميم عصري...")

    try:
        تطبيق = تطبيق_الصيانة_المصري()
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎉 استمتع بأفضل تجربة مصرية عصرية!")
        تطبيق.mainloop()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل التطبيق:\n{e}")

if __name__ == "__main__":
    main()
