"""
منطق حساب السعرات الحرارية باستخدام معادلة هاريس-بندكت
"""

# ...existing code...

def calculate_bmr(age, weight, height, gender):
    """
    حساب معدل الأيض الأساسي (BMR) حسب الجنس
    """
    if gender == 'Male':
        return 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age)
    else:
        return 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age)


def calculate_daily_calories(bmr, activity_level):
    """
    حساب السعرات اليومية حسب مستوى النشاط
    """
    activity_factors = {
        'Sedentary': 1.2,
        'Lightly active': 1.375,
        'Moderately active': 1.55,
        'Very active': 1.725,
        'Extra active': 1.9
    }
    return bmr * activity_factors.get(activity_level, 1.2)


def adjust_calories_for_goal(calories, goal):
    """
    تعديل السعرات حسب الهدف
    """
    if goal == 'Lose Weight':
        return calories - 500
    elif goal == 'Gain Weight':
        return calories + 500
    return calories

# ...existing code...
