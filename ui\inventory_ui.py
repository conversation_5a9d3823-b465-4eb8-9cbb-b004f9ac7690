"""
واجهة إدارة المخزون وقطع الغيار
Inventory management UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class InventoryFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8f9fa')
        self.create_widgets()
        self.load_inventory()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="إدارة المخزون وقطع الغيار",
                font=("Arial", 20, "bold"),
                bg='#2c3e50', fg='white').pack(pady=15)

        # إطار البحث والإضافة
        search_frame = tk.Frame(self, bg='#f8f9fa')
        search_frame.pack(pady=10, padx=20, fill='x')

        # البحث
        search_left = tk.Frame(search_frame, bg='#f8f9fa')
        search_left.pack(side=tk.LEFT)

        tk.Label(search_left, text="البحث:", font=("Arial", 12),
                bg='#f8f9fa').pack(side=tk.LEFT, padx=(0, 5))

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_left, textvariable=self.search_var,
                               font=("Arial", 11), width=25)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind('<KeyRelease>', lambda e: self.search_inventory())

        tk.Button(search_left, text="بحث", command=self.search_inventory,
                 bg='#3498db', fg='white', font=("Arial", 10, "bold"),
                 padx=15).pack(side=tk.LEFT, padx=5)

        tk.Button(search_left, text="إعادة تحميل", command=self.load_inventory,
                 bg='#95a5a6', fg='white', font=("Arial", 10, "bold"),
                 padx=15).pack(side=tk.LEFT, padx=5)

        # أزرار الإضافة
        search_right = tk.Frame(search_frame, bg='#f8f9fa')
        search_right.pack(side=tk.RIGHT)

        tk.Button(search_right, text="إضافة قطعة جديدة", command=self.add_item_dialog,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=20, pady=5).pack(side=tk.RIGHT, padx=5)

        tk.Button(search_right, text="تنبيهات المخزون", command=self.show_low_stock_alerts,
                 bg='#e67e22', fg='white', font=("Arial", 12, "bold"),
                 padx=20, pady=5).pack(side=tk.RIGHT, padx=5)

        # جدول المخزون
        table_frame = tk.Frame(self, bg='#f8f9fa')
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إعداد الجدول
        columns = ("id", "name", "part_number", "quantity", "price", "low_stock_threshold", "status")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        headers = {
            "id": "الرقم",
            "name": "اسم القطعة",
            "part_number": "رقم القطعة",
            "quantity": "الكمية",
            "price": "السعر",
            "low_stock_threshold": "حد التنبيه",
            "status": "الحالة"
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == "id":
                self.tree.column(col, width=80, anchor='center')
            elif col == "name":
                self.tree.column(col, width=200)
            elif col == "part_number":
                self.tree.column(col, width=150, anchor='center')
            elif col in ["quantity", "price", "low_stock_threshold"]:
                self.tree.column(col, width=100, anchor='center')
            elif col == "status":
                self.tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # أزرار العمليات
        btn_frame = tk.Frame(self, bg='#f8f9fa')
        btn_frame.pack(pady=10)

        buttons = [
            ("تعديل", self.edit_item_dialog, "#f39c12"),
            ("حذف", self.delete_item, "#e74c3c"),
            ("تحديث الكمية", self.update_quantity_dialog, "#3498db"),
            ("تقرير المخزون", self.generate_inventory_report, "#9b59b6")
        ]

        for text, command, color in buttons:
            tk.Button(btn_frame, text=text, command=command,
                     bg=color, fg='white', font=("Arial", 11, "bold"),
                     padx=15, pady=5).pack(side=tk.LEFT, padx=5)

        # إحصائيات سريعة
        stats_frame = tk.Frame(self, bg='#ecf0f1', relief='ridge', bd=2)
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))

        stats_inner = tk.Frame(stats_frame, bg='#ecf0f1')
        stats_inner.pack(pady=10)

        self.total_items_label = tk.Label(stats_inner, text="إجمالي القطع: 0",
                                         font=("Arial", 12, "bold"),
                                         bg='#ecf0f1', fg='#2c3e50')
        self.total_items_label.pack(side=tk.LEFT, padx=20)

        self.low_stock_label = tk.Label(stats_inner, text="تنبيهات المخزون: 0",
                                       font=("Arial", 12, "bold"),
                                       bg='#ecf0f1', fg='#e74c3c')
        self.low_stock_label.pack(side=tk.LEFT, padx=20)

        self.total_value_label = tk.Label(stats_inner, text="القيمة الإجمالية: 0",
                                         font=("Arial", 12, "bold"),
                                         bg='#ecf0f1', fg='#27ae60')
        self.total_value_label.pack(side=tk.LEFT, padx=20)

    def load_inventory(self, search=None):
        """تحميل بيانات المخزون"""
        # مسح البيانات الحالية
        for row in self.tree.get_children():
            self.tree.delete(row)

        try:
            conn = get_connection()
            c = conn.cursor()

            if search:
                c.execute("""SELECT id, name, part_number, quantity, price, low_stock_threshold
                           FROM inventory
                           WHERE name LIKE ? OR part_number LIKE ?""",
                         (f"%{search}%", f"%{search}%"))
            else:
                c.execute("""SELECT id, name, part_number, quantity, price, low_stock_threshold
                           FROM inventory ORDER BY name""")

            items = c.fetchall()
            total_value = 0
            low_stock_count = 0

            for row in items:
                item_id, name, part_number, quantity, price, threshold = row

                # تحديد حالة المخزون
                if quantity <= threshold:
                    status = "نفد المخزون" if quantity == 0 else "مخزون منخفض"
                    low_stock_count += 1
                else:
                    status = "متوفر"

                # حساب القيمة الإجمالية
                total_value += quantity * price

                # إدراج البيانات مع الحالة
                display_row = row + (status,)
                item = self.tree.insert('', 'end', values=display_row)

                # تلوين الصفوف حسب الحالة
                if quantity == 0:
                    self.tree.set(item, "status", "نفد المخزون")
                elif quantity <= threshold:
                    self.tree.set(item, "status", "مخزون منخفض")

            # تحديث الإحصائيات
            self.total_items_label.config(text=f"إجمالي القطع: {len(items)}")
            self.low_stock_label.config(text=f"تنبيهات المخزون: {low_stock_count}")
            self.total_value_label.config(text=f"القيمة الإجمالية: {total_value:.2f}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def search_inventory(self):
        """البحث في المخزون"""
        search_term = self.search_var.get().strip()
        self.load_inventory(search_term if search_term else None)

    def add_item_dialog(self):
        """فتح نافذة إضافة قطعة جديدة"""
        self.item_dialog()

    def edit_item_dialog(self):
        """فتح نافذة تعديل القطعة المحددة"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد قطعة للتعديل.")
            return
        values = self.tree.item(selected[0], 'values')
        self.item_dialog(values)

    def item_dialog(self, values=None):
        """نافذة إضافة/تعديل قطعة المخزون"""
        win = tk.Toplevel(self)
        win.title("إضافة قطعة جديدة" if not values else "تعديل بيانات القطعة")
        win.geometry("500x450")
        win.resizable(False, False)
        win.configure(bg='#f8f9fa')

        # توسيط النافذة
        win.transient(self)
        win.grab_set()

        # العنوان
        title_text = "إضافة قطعة جديدة" if not values else "تعديل بيانات القطعة"
        tk.Label(win, text=title_text, font=("Arial", 16, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(win, bg='#f8f9fa')
        fields_frame.pack(pady=20, padx=40, fill='both', expand=True)

        # الحقول
        fields = [
            ("اسم القطعة:", "name"),
            ("رقم القطعة:", "part_number"),
            ("الكمية:", "quantity"),
            ("السعر:", "price"),
            ("حد التنبيه:", "low_stock_threshold")
        ]

        self.field_vars = {}

        for i, (label_text, field_name) in enumerate(fields):
            # التسمية
            tk.Label(fields_frame, text=label_text, font=("Arial", 12, "bold"),
                    bg='#f8f9fa', fg='#2c3e50').grid(row=i, column=0, sticky='e', padx=10, pady=15)

            # الحقل
            var = tk.StringVar()
            if values and i < len(values)-2:  # تجاهل id و status
                var.set(values[i+1])

            entry = tk.Entry(fields_frame, textvariable=var, font=("Arial", 11), width=30)
            entry.grid(row=i, column=1, padx=10, pady=15, sticky='ew')
            self.field_vars[field_name] = var

        # أزرار الحفظ والإلغاء
        btn_frame = tk.Frame(win, bg='#f8f9fa')
        btn_frame.pack(pady=20)

        def save_item():
            """حفظ بيانات القطعة"""
            try:
                # جمع البيانات
                name = self.field_vars["name"].get().strip()
                part_number = self.field_vars["part_number"].get().strip()
                quantity = self.field_vars["quantity"].get().strip()
                price = self.field_vars["price"].get().strip()
                threshold = self.field_vars["low_stock_threshold"].get().strip()

                # التحقق من صحة البيانات
                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم القطعة")
                    return

                if not part_number:
                    messagebox.showerror("خطأ", "يرجى إدخال رقم القطعة")
                    return

                try:
                    quantity = int(quantity)
                    price = float(price)
                    threshold = int(threshold)
                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للكمية والسعر وحد التنبيه")
                    return

                if quantity < 0 or price < 0 or threshold < 0:
                    messagebox.showerror("خطأ", "القيم يجب أن تكون موجبة")
                    return

                # حفظ في قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                if values:  # تعديل
                    c.execute("""UPDATE inventory
                               SET name=?, part_number=?, quantity=?, price=?, low_stock_threshold=?
                               WHERE id=?""",
                             (name, part_number, quantity, price, threshold, values[0]))
                    messagebox.showinfo("نجح", "تم تحديث بيانات القطعة بنجاح")
                else:  # إضافة جديد
                    c.execute("""INSERT INTO inventory (name, part_number, quantity, price, low_stock_threshold)
                               VALUES (?, ?, ?, ?, ?)""",
                             (name, part_number, quantity, price, threshold))
                    messagebox.showinfo("نجح", "تم إضافة القطعة بنجاح")

                conn.commit()
                conn.close()

                # تحديث الجدول وإغلاق النافذة
                self.load_inventory()
                win.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ البيانات:\n{str(e)}")

        tk.Button(btn_frame, text="حفظ", command=save_item,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

        tk.Button(btn_frame, text="إلغاء", command=win.destroy,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

    def delete_item(self):
        """حذف القطعة المحددة"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد قطعة للحذف.")
            return

        values = self.tree.item(selected[0], 'values')
        item_name = values[1]

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف القطعة '{item_name}'؟"):
            try:
                conn = get_connection()
                c = conn.cursor()

                # حذف القطعة
                c.execute("DELETE FROM inventory WHERE id=?", (values[0],))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم حذف القطعة '{item_name}' بنجاح")
                self.load_inventory()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف القطعة:\n{str(e)}")

    def update_quantity_dialog(self):
        """نافذة تحديث كمية القطعة"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد قطعة لتحديث كميتها.")
            return

        values = self.tree.item(selected[0], 'values')
        item_name = values[1]
        current_quantity = values[3]

        # إنشاء نافذة التحديث
        win = tk.Toplevel(self)
        win.title(f"تحديث كمية - {item_name}")
        win.geometry("400x300")
        win.resizable(False, False)
        win.configure(bg='#f8f9fa')

        # توسيط النافذة
        win.transient(self)
        win.grab_set()

        # العنوان
        tk.Label(win, text=f"تحديث كمية: {item_name}",
                font=("Arial", 16, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # الكمية الحالية
        tk.Label(win, text=f"الكمية الحالية: {current_quantity}",
                font=("Arial", 12),
                bg='#f8f9fa', fg='#7f8c8d').pack(pady=10)

        # إطار التحديث
        update_frame = tk.Frame(win, bg='#f8f9fa')
        update_frame.pack(pady=20)

        # نوع العملية
        operation_var = tk.StringVar(value="add")
        tk.Radiobutton(update_frame, text="إضافة كمية", variable=operation_var, value="add",
                      bg='#f8f9fa', font=("Arial", 11)).pack(anchor='w')
        tk.Radiobutton(update_frame, text="خصم كمية", variable=operation_var, value="subtract",
                      bg='#f8f9fa', font=("Arial", 11)).pack(anchor='w')
        tk.Radiobutton(update_frame, text="تعيين كمية جديدة", variable=operation_var, value="set",
                      bg='#f8f9fa', font=("Arial", 11)).pack(anchor='w')

        # الكمية
        tk.Label(update_frame, text="الكمية:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=(20, 5))

        quantity_var = tk.StringVar()
        tk.Entry(update_frame, textvariable=quantity_var, font=("Arial", 12),
                width=20, justify='center').pack(pady=5)

        # سبب التحديث
        tk.Label(update_frame, text="سبب التحديث:", font=("Arial", 12, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=(20, 5))

        reason_var = tk.StringVar()
        reason_combo = ttk.Combobox(update_frame, textvariable=reason_var,
                                   values=["استلام بضاعة جديدة", "بيع", "تلف", "جرد", "أخرى"],
                                   state="readonly", width=25)
        reason_combo.pack(pady=5)
        reason_combo.set("استلام بضاعة جديدة")

        def update_quantity():
            """تحديث الكمية"""
            try:
                quantity = int(quantity_var.get())
                operation = operation_var.get()
                reason = reason_var.get()

                if quantity <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                    return

                current_qty = int(current_quantity)

                if operation == "add":
                    new_quantity = current_qty + quantity
                elif operation == "subtract":
                    new_quantity = current_qty - quantity
                    if new_quantity < 0:
                        messagebox.showerror("خطأ", "لا يمكن أن تكون الكمية سالبة")
                        return
                else:  # set
                    new_quantity = quantity

                # تحديث قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                c.execute("UPDATE inventory SET quantity=? WHERE id=?",
                         (new_quantity, values[0]))

                # إضافة سجل للحركة (يمكن إضافة جدول منفصل للحركات لاحقاً)

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم تحديث الكمية بنجاح\nالكمية الجديدة: {new_quantity}")
                self.load_inventory()
                win.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في التحديث:\n{str(e)}")

        # أزرار التحديث والإلغاء
        btn_frame = tk.Frame(win, bg='#f8f9fa')
        btn_frame.pack(pady=20)

        tk.Button(btn_frame, text="تحديث", command=update_quantity,
                 bg='#3498db', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

        tk.Button(btn_frame, text="إلغاء", command=win.destroy,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

    def show_low_stock_alerts(self):
        """عرض تنبيهات المخزون المنخفض"""
        try:
            conn = get_connection()
            c = conn.cursor()

            c.execute("""SELECT name, part_number, quantity, low_stock_threshold
                        FROM inventory
                        WHERE quantity <= low_stock_threshold
                        ORDER BY quantity ASC""")

            low_stock_items = c.fetchall()
            conn.close()

            if not low_stock_items:
                messagebox.showinfo("تنبيهات المخزون", "لا توجد قطع تحتاج إلى تنبيه")
                return

            # إنشاء نافذة التنبيهات
            alert_win = tk.Toplevel(self)
            alert_win.title("تنبيهات المخزون المنخفض")
            alert_win.geometry("700x400")
            alert_win.configure(bg='#f8f9fa')

            # العنوان
            tk.Label(alert_win, text="تنبيهات المخزون المنخفض",
                    font=("Arial", 18, "bold"),
                    bg='#f8f9fa', fg='#e74c3c').pack(pady=20)

            # جدول التنبيهات
            columns = ("name", "part_number", "quantity", "threshold", "status")
            alert_tree = ttk.Treeview(alert_win, columns=columns, show="headings", height=12)

            headers = {
                "name": "اسم القطعة",
                "part_number": "رقم القطعة",
                "quantity": "الكمية الحالية",
                "threshold": "حد التنبيه",
                "status": "الحالة"
            }

            for col in columns:
                alert_tree.heading(col, text=headers[col])
                alert_tree.column(col, width=130)

            # إدراج البيانات
            for item in low_stock_items:
                name, part_number, quantity, threshold = item
                status = "نفد المخزون" if quantity == 0 else "مخزون منخفض"
                alert_tree.insert('', 'end', values=(name, part_number, quantity, threshold, status))

            alert_tree.pack(fill="both", expand=True, padx=20, pady=10)

            # رسالة تحذيرية
            warning_text = f"تم العثور على {len(low_stock_items)} قطعة تحتاج إلى إعادة تموين"
            tk.Label(alert_win, text=warning_text,
                    font=("Arial", 12, "bold"),
                    bg='#f8f9fa', fg='#e74c3c').pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في عرض التنبيهات:\n{str(e)}")

    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        try:
            conn = get_connection()
            c = conn.cursor()

            # إحصائيات عامة
            c.execute("SELECT COUNT(*), SUM(quantity * price) FROM inventory")
            total_items, total_value = c.fetchone()

            c.execute("SELECT COUNT(*) FROM inventory WHERE quantity <= low_stock_threshold")
            low_stock_count = c.fetchone()[0]

            c.execute("SELECT COUNT(*) FROM inventory WHERE quantity = 0")
            out_of_stock_count = c.fetchone()[0]

            conn.close()

            # إنشاء نافذة التقرير
            report_win = tk.Toplevel(self)
            report_win.title("تقرير المخزون")
            report_win.geometry("600x500")
            report_win.configure(bg='white')

            # محتوى التقرير
            report_content = f"""
            تقرير المخزون الشامل
            ═══════════════════════════════════════

            الإحصائيات العامة:
            • إجمالي القطع: {total_items or 0}
            • القيمة الإجمالية: {total_value or 0:.2f} ريال
            • قطع تحتاج تنبيه: {low_stock_count or 0}
            • قطع نفد مخزونها: {out_of_stock_count or 0}

            ═══════════════════════════════════════
            تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            ملاحظات:
            - يُنصح بمراجعة القطع التي نفد مخزونها
            - تحديث حدود التنبيه حسب الحاجة
            - مراجعة دورية للمخزون كل أسبوع
            """

            text_widget = tk.Text(report_win, font=("Arial", 12), bg='white', wrap='word')
            text_widget.pack(fill='both', expand=True, padx=20, pady=20)
            text_widget.insert('1.0', report_content)
            text_widget.config(state='disabled')

            # أزرار التقرير
            btn_frame = tk.Frame(report_win, bg='white')
            btn_frame.pack(pady=10)

            tk.Button(btn_frame, text="طباعة التقرير",
                     command=lambda: messagebox.showinfo("طباعة", "تم إرسال التقرير للطباعة"),
                     bg='#3498db', fg='white', font=("Arial", 12, "bold"),
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)

            tk.Button(btn_frame, text="تصدير إلى ملف",
                     command=lambda: messagebox.showinfo("تصدير", "تم تصدير التقرير بنجاح"),
                     bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء التقرير:\n{str(e)}")
