"""
Customer management UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class CustomerFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.create_widgets()
        self.load_customers()

    def create_widgets(self):
        tk.Label(self, text="Customers", font=("Arial", 18, "bold")).pack(pady=10)
        search_frame = tk.Frame(self)
        search_frame.pack(pady=5)
        tk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        tk.Entry(search_frame, textvariable=self.search_var).pack(side=tk.LEFT, padx=5)
        tk.But<PERSON>(search_frame, text="Search", command=self.search_customers).pack(side=tk.LEFT)
        tk.Button(search_frame, text="Add Customer", command=self.add_customer_dialog).pack(side=tk.LEFT, padx=10)
        self.tree = ttk.Treeview(self, columns=("id", "name", "phone", "email", "address"), show="headings")
        for col in ("id", "name", "phone", "email", "address"):
            self.tree.heading(col, text=col.title())
            self.tree.column(col, width=120)
        self.tree.pack(fill="both", expand=True, pady=10)
        btn_frame = tk.Frame(self)
        btn_frame.pack()
        tk.Button(btn_frame, text="Edit", command=self.edit_customer_dialog).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="Delete", command=self.delete_customer).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="View History", command=self.view_history).pack(side=tk.LEFT, padx=5)

    def load_customers(self, search=None):
        for row in self.tree.get_children():
            self.tree.delete(row)
        conn = get_connection()
        c = conn.cursor()
        if search:
            c.execute("SELECT * FROM customers WHERE name LIKE ? OR phone LIKE ?", (f"%{search}%", f"%{search}%"))
        else:
            c.execute("SELECT * FROM customers")
        for row in c.fetchall():
            self.tree.insert('', 'end', values=row)
        conn.close()

    def search_customers(self):
        self.load_customers(self.search_var.get())

    def add_customer_dialog(self):
        self.customer_dialog()

    def edit_customer_dialog(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Select", "Please select a customer to edit.")
            return
        values = self.tree.item(selected[0], 'values')
        self.customer_dialog(values)

    def customer_dialog(self, values=None):
        win = tk.Toplevel(self)
        win.title("Add/Edit Customer")
        fields = ["Name", "Phone", "Email", "Address"]
        vars = [tk.StringVar(value=values[i+1] if values else "") for i in range(4)]
        for i, field in enumerate(fields):
            tk.Label(win, text=field).grid(row=i, column=0, padx=5, pady=5)
            tk.Entry(win, textvariable=vars[i]).grid(row=i, column=1, padx=5, pady=5)
        def save():
            name, phone, email, address = [v.get() for v in vars]
            conn = get_connection()
            c = conn.cursor()
            if values:
                c.execute("UPDATE customers SET name=?, phone=?, email=?, address=? WHERE id=?", (name, phone, email, address, values[0]))
            else:
                c.execute("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)", (name, phone, email, address))
            conn.commit()
            conn.close()
            self.load_customers()
            win.destroy()
        tk.Button(win, text="Save", command=save).grid(row=4, column=0, columnspan=2, pady=10)

    def delete_customer(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Select", "Please select a customer to delete.")
            return
        values = self.tree.item(selected[0], 'values')
        if messagebox.askyesno("Delete", f"Delete customer {values[1]}?"):
            conn = get_connection()
            c = conn.cursor()
            c.execute("DELETE FROM customers WHERE id=?", (values[0],))
            conn.commit()
            conn.close()
            self.load_customers()

    def view_history(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Select", "Please select a customer.")
            return
        values = self.tree.item(selected[0], 'values')
        win = tk.Toplevel(self)
        win.title(f"Repair History for {values[1]}")
        tree = ttk.Treeview(win, columns=("OrderID", "Device", "Issue", "Status", "Cost"), show="headings")
        for col in ("OrderID", "Device", "Issue", "Status", "Cost"):
            tree.heading(col, text=col)
            tree.column(col, width=100)
        tree.pack(fill="both", expand=True)
        conn = get_connection()
        c = conn.cursor()
        c.execute("SELECT id, device_brand || ' ' || device_model, issue, status, estimated_cost FROM orders WHERE customer_id=?", (values[0],))
        for row in c.fetchall():
            tree.insert('', 'end', values=row)
        conn.close()
