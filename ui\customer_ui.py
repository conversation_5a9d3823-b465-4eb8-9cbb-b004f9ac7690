"""
واجهة إدارة العملاء
Customer management UI
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime
import re

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class CustomerFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8f9fa')
        self.create_widgets()
        self.load_customers()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)

        tk.Label(title_frame, text="إدارة العملاء",
                font=("Arial", 20, "bold"),
                bg='#2c3e50', fg='white').pack(pady=15)

        # إطار البحث والإضافة
        search_frame = tk.Frame(self, bg='#f8f9fa')
        search_frame.pack(pady=10, padx=20, fill='x')

        # البحث
        search_left = tk.Frame(search_frame, bg='#f8f9fa')
        search_left.pack(side=tk.LEFT)

        tk.Label(search_left, text="البحث:", font=("Arial", 12),
                bg='#f8f9fa').pack(side=tk.LEFT, padx=(0, 5))

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_left, textvariable=self.search_var,
                               font=("Arial", 11), width=25)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind('<KeyRelease>', lambda e: self.search_customers())

        tk.Button(search_left, text="بحث", command=self.search_customers,
                 bg='#3498db', fg='white', font=("Arial", 10, "bold"),
                 padx=15).pack(side=tk.LEFT, padx=5)

        tk.Button(search_left, text="إعادة تحميل", command=self.load_customers,
                 bg='#95a5a6', fg='white', font=("Arial", 10, "bold"),
                 padx=15).pack(side=tk.LEFT, padx=5)

        # أزرار الإضافة
        search_right = tk.Frame(search_frame, bg='#f8f9fa')
        search_right.pack(side=tk.RIGHT)

        tk.Button(search_right, text="إضافة عميل جديد", command=self.add_customer_dialog,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=20, pady=5).pack(side=tk.RIGHT)

        # جدول العملاء
        table_frame = tk.Frame(self, bg='#f8f9fa')
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إعداد الجدول
        columns = ("id", "name", "phone", "email", "address", "created_date")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        headers = {
            "id": "الرقم",
            "name": "الاسم",
            "phone": "الهاتف",
            "email": "البريد الإلكتروني",
            "address": "العنوان",
            "created_date": "تاريخ الإضافة"
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == "id":
                self.tree.column(col, width=80, anchor='center')
            elif col == "name":
                self.tree.column(col, width=150)
            elif col == "phone":
                self.tree.column(col, width=120, anchor='center')
            elif col == "email":
                self.tree.column(col, width=200)
            elif col == "address":
                self.tree.column(col, width=200)
            elif col == "created_date":
                self.tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # أزرار العمليات
        btn_frame = tk.Frame(self, bg='#f8f9fa')
        btn_frame.pack(pady=10)

        buttons = [
            ("تعديل", self.edit_customer_dialog, "#f39c12"),
            ("حذف", self.delete_customer, "#e74c3c"),
            ("عرض السجل", self.view_history, "#9b59b6"),
            ("طباعة بيانات العميل", self.print_customer_info, "#34495e")
        ]

        for text, command, color in buttons:
            tk.Button(btn_frame, text=text, command=command,
                     bg=color, fg='white', font=("Arial", 11, "bold"),
                     padx=15, pady=5).pack(side=tk.LEFT, padx=5)

        # إحصائيات سريعة
        stats_frame = tk.Frame(self, bg='#ecf0f1', relief='ridge', bd=2)
        stats_frame.pack(fill='x', padx=20, pady=(0, 10))

        self.stats_label = tk.Label(stats_frame, text="إجمالي العملاء: 0",
                                   font=("Arial", 12, "bold"),
                                   bg='#ecf0f1', fg='#2c3e50')
        self.stats_label.pack(pady=10)

    def load_customers(self, search=None):
        """تحميل بيانات العملاء"""
        # مسح البيانات الحالية
        for row in self.tree.get_children():
            self.tree.delete(row)

        try:
            conn = get_connection()
            c = conn.cursor()

            if search:
                c.execute("""SELECT id, name, phone, email, address,
                           datetime('now', 'localtime') as created_date
                           FROM customers
                           WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?""",
                         (f"%{search}%", f"%{search}%", f"%{search}%"))
            else:
                c.execute("""SELECT id, name, phone, email, address,
                           datetime('now', 'localtime') as created_date
                           FROM customers ORDER BY id DESC""")

            customers = c.fetchall()
            for row in customers:
                self.tree.insert('', 'end', values=row)

            # تحديث الإحصائيات
            self.stats_label.config(text=f"إجمالي العملاء: {len(customers)}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات:\n{str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    def search_customers(self):
        """البحث عن العملاء"""
        search_term = self.search_var.get().strip()
        self.load_customers(search_term if search_term else None)

    def add_customer_dialog(self):
        """فتح نافذة إضافة عميل جديد"""
        self.customer_dialog()

    def edit_customer_dialog(self):
        """فتح نافذة تعديل العميل المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للتعديل.")
            return
        values = self.tree.item(selected[0], 'values')
        self.customer_dialog(values)

    def customer_dialog(self, values=None):
        """نافذة إضافة/تعديل العميل"""
        win = tk.Toplevel(self)
        win.title("إضافة عميل جديد" if not values else "تعديل بيانات العميل")
        win.geometry("500x400")
        win.resizable(False, False)
        win.configure(bg='#f8f9fa')

        # توسيط النافذة
        win.transient(self)
        win.grab_set()

        # العنوان
        title_text = "إضافة عميل جديد" if not values else "تعديل بيانات العميل"
        tk.Label(win, text=title_text, font=("Arial", 16, "bold"),
                bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(win, bg='#f8f9fa')
        fields_frame.pack(pady=20, padx=40, fill='both', expand=True)

        # الحقول
        fields = [
            ("الاسم الكامل:", "name"),
            ("رقم الهاتف:", "phone"),
            ("البريد الإلكتروني:", "email"),
            ("العنوان:", "address")
        ]

        self.field_vars = {}

        for i, (label_text, field_name) in enumerate(fields):
            # التسمية
            tk.Label(fields_frame, text=label_text, font=("Arial", 12, "bold"),
                    bg='#f8f9fa', fg='#2c3e50').grid(row=i, column=0, sticky='e', padx=10, pady=10)

            # الحقل
            var = tk.StringVar()
            if values and i < len(values)-2:  # تجاهل id و created_date
                var.set(values[i+1])

            if field_name == "address":
                # حقل نص متعدد الأسطر للعنوان
                text_widget = tk.Text(fields_frame, height=3, width=30, font=("Arial", 11))
                text_widget.grid(row=i, column=1, padx=10, pady=10, sticky='ew')
                if values and i < len(values)-2:
                    text_widget.insert('1.0', values[i+1])
                self.field_vars[field_name] = text_widget
            else:
                entry = tk.Entry(fields_frame, textvariable=var, font=("Arial", 11), width=30)
                entry.grid(row=i, column=1, padx=10, pady=10, sticky='ew')
                self.field_vars[field_name] = var

        # أزرار الحفظ والإلغاء
        btn_frame = tk.Frame(win, bg='#f8f9fa')
        btn_frame.pack(pady=20)

        def save_customer():
            """حفظ بيانات العميل"""
            try:
                # جمع البيانات
                name = self.field_vars["name"].get().strip()
                phone = self.field_vars["phone"].get().strip()
                email = self.field_vars["email"].get().strip()

                # الحصول على النص من Text widget
                if isinstance(self.field_vars["address"], tk.Text):
                    address = self.field_vars["address"].get('1.0', 'end-1c').strip()
                else:
                    address = self.field_vars["address"].get().strip()

                # التحقق من صحة البيانات
                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                    return

                if not phone:
                    messagebox.showerror("خطأ", "يرجى إدخال رقم الهاتف")
                    return

                # التحقق من صحة رقم الهاتف
                if not re.match(r'^[\d\s\-\+\(\)]+$', phone):
                    messagebox.showerror("خطأ", "رقم الهاتف غير صحيح")
                    return

                # التحقق من صحة البريد الإلكتروني
                if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
                    return

                # حفظ في قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                if values:  # تعديل
                    c.execute("""UPDATE customers
                               SET name=?, phone=?, email=?, address=?
                               WHERE id=?""",
                             (name, phone, email, address, values[0]))
                    messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                else:  # إضافة جديد
                    c.execute("""INSERT INTO customers (name, phone, email, address)
                               VALUES (?, ?, ?, ?)""",
                             (name, phone, email, address))
                    messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")

                conn.commit()
                conn.close()

                # تحديث الجدول وإغلاق النافذة
                self.load_customers()
                win.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ البيانات:\n{str(e)}")

        tk.Button(btn_frame, text="حفظ", command=save_customer,
                 bg='#27ae60', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

        tk.Button(btn_frame, text="إلغاء", command=win.destroy,
                 bg='#e74c3c', fg='white', font=("Arial", 12, "bold"),
                 padx=30, pady=8).pack(side=tk.LEFT, padx=10)

    def delete_customer(self):
        """حذف العميل المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للحذف.")
            return

        values = self.tree.item(selected[0], 'values')
        customer_name = values[1]

        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف العميل '{customer_name}'؟\n"
                              "سيتم حذف جميع طلبات الصيانة المرتبطة به أيضاً."):
            try:
                conn = get_connection()
                c = conn.cursor()

                # حذف طلبات الصيانة المرتبطة أولاً
                c.execute("DELETE FROM orders WHERE customer_id=?", (values[0],))

                # حذف العميل
                c.execute("DELETE FROM customers WHERE id=?", (values[0],))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم حذف العميل '{customer_name}' بنجاح")
                self.load_customers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف العميل:\n{str(e)}")

    def view_history(self):
        """عرض سجل طلبات الصيانة للعميل"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل لعرض سجله.")
            return

        values = self.tree.item(selected[0], 'values')
        customer_name = values[1]
        customer_id = values[0]

        # إنشاء نافذة السجل
        history_win = tk.Toplevel(self)
        history_win.title(f"سجل طلبات الصيانة - {customer_name}")
        history_win.geometry("900x500")
        history_win.configure(bg='#f8f9fa')

        # العنوان
        tk.Label(history_win, text=f"سجل طلبات الصيانة للعميل: {customer_name}",
                font=("Arial", 16, "bold"), bg='#f8f9fa', fg='#2c3e50').pack(pady=20)

        # جدول السجل
        columns = ("order_id", "device", "issue", "status", "cost", "technician", "created_date")
        history_tree = ttk.Treeview(history_win, columns=columns, show="headings", height=15)

        headers = {
            "order_id": "رقم الطلب",
            "device": "الجهاز",
            "issue": "المشكلة",
            "status": "الحالة",
            "cost": "التكلفة",
            "technician": "الفني",
            "created_date": "تاريخ الطلب"
        }

        for col in columns:
            history_tree.heading(col, text=headers[col])
            history_tree.column(col, width=120)

        # شريط التمرير
        scrollbar_h = ttk.Scrollbar(history_win, orient="vertical", command=history_tree.yview)
        history_tree.configure(yscrollcommand=scrollbar_h.set)

        history_tree.pack(side="left", fill="both", expand=True, padx=20, pady=10)
        scrollbar_h.pack(side="right", fill="y", pady=10)

        # تحميل البيانات
        try:
            conn = get_connection()
            c = conn.cursor()
            c.execute("""SELECT id, device_brand || ' ' || device_model, issue, status,
                        estimated_cost, technician, created_at
                        FROM orders WHERE customer_id=? ORDER BY created_at DESC""",
                     (customer_id,))

            orders = c.fetchall()
            for row in orders:
                history_tree.insert('', 'end', values=row)

            conn.close()

            if not orders:
                tk.Label(history_win, text="لا توجد طلبات صيانة لهذا العميل",
                        font=("Arial", 14), bg='#f8f9fa', fg='#7f8c8d').pack(pady=50)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل السجل:\n{str(e)}")

    def print_customer_info(self):
        """طباعة بيانات العميل"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للطباعة.")
            return

        values = self.tree.item(selected[0], 'values')

        # إنشاء نافذة معاينة الطباعة
        print_win = tk.Toplevel(self)
        print_win.title("معاينة الطباعة - بيانات العميل")
        print_win.geometry("600x400")
        print_win.configure(bg='white')

        # محتوى الطباعة
        content = f"""
        بيانات العميل
        ═══════════════════════════════════════

        الرقم: {values[0]}
        الاسم: {values[1]}
        الهاتف: {values[2]}
        البريد الإلكتروني: {values[3]}
        العنوان: {values[4]}
        تاريخ الإضافة: {values[5] if len(values) > 5 else 'غير محدد'}

        ═══════════════════════════════════════
        تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        text_widget = tk.Text(print_win, font=("Arial", 12), bg='white', wrap='word')
        text_widget.pack(fill='both', expand=True, padx=20, pady=20)
        text_widget.insert('1.0', content)
        text_widget.config(state='disabled')

        # زر الطباعة
        tk.Button(print_win, text="طباعة",
                 command=lambda: messagebox.showinfo("طباعة", "تم إرسال المستند للطباعة"),
                 bg='#3498db', fg='white', font=("Arial", 12, "bold"),
                 padx=20, pady=5).pack(pady=10)
