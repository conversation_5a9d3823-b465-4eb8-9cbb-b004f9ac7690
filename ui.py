"""
واجهة المستخدم باستخدام Tkinter
"""
import tkinter as tk
from tkinter import ttk, messagebox
from logic import calculate_bmr, calculate_daily_calories, adjust_calories_for_goal
from database import save_result, get_history
from themes import current_theme, toggle_theme
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class CalorieCalculatorApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title('حاسبة السعرات الحرارية')
        self.geometry('700x700')
        self.resizable(False, False)
        self.configure(bg=current_theme['bg'])
        self.create_widgets()
        self.apply_theme()

    def create_widgets(self):
        # العنوان
        self.title_label = tk.Label(self, text='حاسبة السعرات الحرارية', font=('Cairo', 22, 'bold'))
        self.title_label.pack(pady=15)

        # إطار الإدخال
        self.input_frame = tk.Frame(self, bg=current_theme['bg'])
        self.input_frame.pack(pady=10)

        # العمر
        tk.Label(self.input_frame, text='العمر (سنة):', bg=current_theme['bg'], fg=current_theme['fg']).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.age_var = tk.IntVar()
        self.age_entry = tk.Entry(self.input_frame, textvariable=self.age_var)
        self.age_entry.grid(row=0, column=1, padx=5, pady=5)

        # الوزن
        tk.Label(self.input_frame, text='الوزن (كجم):', bg=current_theme['bg'], fg=current_theme['fg']).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.weight_var = tk.DoubleVar()
        self.weight_entry = tk.Entry(self.input_frame, textvariable=self.weight_var)
        self.weight_entry.grid(row=1, column=1, padx=5, pady=5)

        # الطول
        tk.Label(self.input_frame, text='الطول (سم):', bg=current_theme['bg'], fg=current_theme['fg']).grid(row=2, column=0, sticky='e', padx=5, pady=5)
        self.height_var = tk.DoubleVar()
        self.height_entry = tk.Entry(self.input_frame, textvariable=self.height_var)
        self.height_entry.grid(row=2, column=1, padx=5, pady=5)

        # الجنس
        tk.Label(self.input_frame, text='الجنس:', bg=current_theme['bg'], fg=current_theme['fg']).grid(row=3, column=0, sticky='e', padx=5, pady=5)
        self.gender_var = tk.StringVar(value='Male')
        self.gender_combo = ttk.Combobox(self.input_frame, textvariable=self.gender_var, values=['Male', 'Female'], state='readonly')
        self.gender_combo.grid(row=3, column=1, padx=5, pady=5)

        # مستوى النشاط
        tk.Label(self.input_frame, text='مستوى النشاط:', bg=current_theme['bg'], fg=current_theme['fg']).grid(row=4, column=0, sticky='e', padx=5, pady=5)
        self.activity_var = tk.StringVar(value='Sedentary')
        self.activity_combo = ttk.Combobox(self.input_frame, textvariable=self.activity_var, values=[
            'Sedentary', 'Lightly active', 'Moderately active', 'Very active', 'Extra active'
        ], state='readonly')
        self.activity_combo.grid(row=4, column=1, padx=5, pady=5)

        # الهدف
        tk.Label(self.input_frame, text='الهدف:', bg=current_theme['bg'], fg=current_theme['fg']).grid(row=5, column=0, sticky='e', padx=5, pady=5)
        self.goal_var = tk.StringVar(value='Maintain Weight')
        self.goal_combo = ttk.Combobox(self.input_frame, textvariable=self.goal_var, values=[
            'Lose Weight', 'Maintain Weight', 'Gain Weight'
        ], state='readonly')
        self.goal_combo.grid(row=5, column=1, padx=5, pady=5)

        # زر حساب السعرات
        self.calc_btn = tk.Button(self, text='احسب السعرات', command=self.calculate, bg=current_theme['button_bg'], fg=current_theme['button_fg'], font=('Cairo', 14, 'bold'))
        self.calc_btn.pack(pady=10)

        # زر تبديل الثيم
        self.theme_btn = tk.Button(self, text='تبديل الوضع (فاتح/داكن)', command=self.toggle_theme, bg=current_theme['button_bg'], fg=current_theme['button_fg'])
        self.theme_btn.pack(pady=5)

        # نتيجة السعرات
        self.result_var = tk.StringVar()
        self.result_label = tk.Label(self, textvariable=self.result_var, font=('Cairo', 16), bg=current_theme['bg'], fg=current_theme['fg'])
        self.result_label.pack(pady=10)

        # أزرار إضافية
        self.copy_btn = tk.Button(self, text='نسخ النتيجة', command=self.copy_result, bg=current_theme['button_bg'], fg=current_theme['button_fg'])
        self.copy_btn.pack(pady=2)
        self.save_btn = tk.Button(self, text='حفظ في السجل', command=self.save_to_history, bg=current_theme['button_bg'], fg=current_theme['button_fg'])
        self.save_btn.pack(pady=2)
        self.show_history_btn = tk.Button(self, text='عرض السجل', command=self.show_history, bg=current_theme['button_bg'], fg=current_theme['button_fg'])
        self.show_history_btn.pack(pady=2)
        self.show_graph_btn = tk.Button(self, text='عرض الرسم البياني الأسبوعي', command=self.show_weekly_graph, bg=current_theme['button_bg'], fg=current_theme['button_fg'])
        self.show_graph_btn.pack(pady=2)

    def apply_theme(self):
        theme = current_theme
        self.configure(bg=theme['bg'])
        self.title_label.config(bg=theme['bg'], fg=theme['fg'])
        self.input_frame.config(bg=theme['bg'])
        for widget in self.input_frame.winfo_children():
            if isinstance(widget, tk.Label):
                widget.config(bg=theme['bg'], fg=theme['fg'])
            elif isinstance(widget, tk.Entry):
                widget.config(bg=theme['entry_bg'], fg=theme['entry_fg'])
            # تجاهل أي عنصر آخر (خاصة ttk)
            # لا تحاول تغيير خصائص ttk.Combobox أو ttk.Entry أو أي عنصر آخر غير مدعوم
        self.calc_btn.config(bg=theme['button_bg'], fg=theme['button_fg'])
        self.theme_btn.config(bg=theme['button_bg'], fg=theme['button_fg'])
        self.result_label.config(bg=theme['bg'], fg=theme['fg'])
        self.copy_btn.config(bg=theme['button_bg'], fg=theme['button_fg'])
        self.save_btn.config(bg=theme['button_bg'], fg=theme['button_fg'])
        self.show_history_btn.config(bg=theme['button_bg'], fg=theme['button_fg'])
        self.show_graph_btn.config(bg=theme['button_bg'], fg=theme['button_fg'])

    def toggle_theme(self):
        toggle_theme()
        self.apply_theme()

    def calculate(self):
        try:
            age = self.age_var.get()
            weight = self.weight_var.get()
            height = self.height_var.get()
            gender = self.gender_var.get()
            activity = self.activity_var.get()
            goal = self.goal_var.get()
            bmr = calculate_bmr(age, weight, height, gender)
            calories = calculate_daily_calories(bmr, activity)
            adjusted = adjust_calories_for_goal(calories, goal)
            self.result_var.set(f'السعرات اليومية المطلوبة: {int(adjusted)} سعر حراري')
            self.last_result = (age, weight, height, gender, activity, goal, adjusted)
        except Exception as e:
            messagebox.showerror('خطأ', f'يرجى التأكد من صحة البيانات\n{e}')

    def copy_result(self):
        self.clipboard_clear()
        self.clipboard_append(self.result_var.get())
        messagebox.showinfo('تم النسخ', 'تم نسخ النتيجة إلى الحافظة')

    def save_to_history(self):
        if hasattr(self, 'last_result'):
            age, weight, height, gender, activity, goal, calories = self.last_result
            save_result(age, weight, height, gender, activity, goal, calories)
            messagebox.showinfo('تم الحفظ', 'تم حفظ النتيجة في السجل')
        else:
            messagebox.showwarning('تنبيه', 'يرجى حساب السعرات أولاً')

    def show_history(self):
        history = get_history()
        win = tk.Toplevel(self)
        win.title('سجل النتائج')
        win.geometry('600x300')
        cols = ['التاريخ', 'العمر', 'الوزن', 'الطول', 'الجنس', 'النشاط', 'الهدف', 'السعرات']
        tree = ttk.Treeview(win, columns=cols, show='headings')
        for col in cols:
            tree.heading(col, text=col)
            tree.column(col, width=70)
        for row in history:
            tree.insert('', 'end', values=row[1:])
        tree.pack(fill='both', expand=True)

    def show_weekly_graph(self):
        if hasattr(self, 'last_result'):
            calories = self.last_result[-1]
            days = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة']
            values = [calories]*7
            fig, ax = plt.subplots(figsize=(6,3))
            ax.bar(days, values, color='#007bff')
            ax.set_ylabel('سعر حراري')
            ax.set_title('توصية السعرات الأسبوعية')
            plt.tight_layout()
            win = tk.Toplevel(self)
            win.title('الرسم البياني الأسبوعي')
            canvas = FigureCanvasTkAgg(fig, master=win)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
        else:
            messagebox.showwarning('تنبيه', 'يرجى حساب السعرات أولاً')

if __name__ == "__main__":
    app = CalorieCalculatorApp()
    app.mainloop()
