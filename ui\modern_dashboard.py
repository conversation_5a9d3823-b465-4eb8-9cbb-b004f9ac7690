"""
واجهة المستخدم العصرية 2025 - لوحة التحكم الرئيسية
Modern UI Dashboard 2025 - Main Control Panel
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime
import math

# إضافة المسارات
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

try:
    from ui.modern_customer_ui import ModernCustomerFrame
    from ui.modern_order_ui import ModernOrderFrame
    from ui.modern_inventory_ui import ModernInventoryFrame
    from ui.modern_report_ui import ModernReportFrame
    from database.db_manager import get_connection
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")

class ModernDashboardApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.setup_modern_window()
        self.create_modern_styles()
        self.create_sidebar()
        self.create_main_content()
        self.create_top_bar()
        self.create_status_bar()
        self.frames = {}
        self.current_frame = None
        self.show_frame("dashboard")
        self.update_time()
        self.animate_startup()

    def setup_modern_window(self):
        """إعداد النافذة بتصميم عصري"""
        self.title("نظام إدارة محل صيانة الجوالات 2025")
        self.geometry("1400x900")
        self.minsize(1200, 800)

        # إزالة الحدود التقليدية
        self.configure(bg='#0f0f23')

        # توسيط النافذة
        self.center_window()

        # تعيين أيقونة مخصصة
        try:
            self.iconbitmap('assets/icon.ico')
        except:
            pass

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = 1400
        height = 900
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def create_modern_styles(self):
        """إنشاء الأنماط العصرية"""
        self.style = ttk.Style()

        # ألوان عصرية 2025
        self.colors = {
            'primary': '#6366f1',      # بنفسجي عصري
            'secondary': '#8b5cf6',    # بنفسجي فاتح
            'accent': '#06b6d4',       # سماوي
            'success': '#10b981',      # أخضر
            'warning': '#f59e0b',      # برتقالي
            'danger': '#ef4444',       # أحمر
            'dark': '#0f0f23',         # أسود عميق
            'dark_secondary': '#1e1e3f', # أسود ثانوي
            'gray': '#6b7280',         # رمادي
            'light_gray': '#f3f4f6',   # رمادي فاتح
            'white': '#ffffff',        # أبيض
            'glass': 'rgba(255,255,255,0.1)', # تأثير زجاجي
        }

        # تكوين الأنماط
        self.style.theme_use('clam')

        # أنماط مخصصة للأزرار
        self.style.configure('Modern.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           font=('Segoe UI', 10, 'bold'))

        self.style.map('Modern.TButton',
                      background=[('active', self.colors['secondary']),
                                ('pressed', self.colors['dark_secondary'])])

    def create_sidebar(self):
        """إنشاء الشريط الجانبي العصري"""
        # الشريط الجانبي الرئيسي
        self.sidebar = tk.Frame(self, bg=self.colors['dark_secondary'], width=280)
        self.sidebar.pack(side='left', fill='y')
        self.sidebar.pack_propagate(False)

        # شعار التطبيق
        logo_frame = tk.Frame(self.sidebar, bg=self.colors['dark_secondary'], height=100)
        logo_frame.pack(fill='x', pady=20)
        logo_frame.pack_propagate(False)

        # أيقونة التطبيق
        app_icon = tk.Label(logo_frame, text="📱", font=('Segoe UI Emoji', 32),
                           bg=self.colors['dark_secondary'], fg=self.colors['primary'])
        app_icon.pack(pady=5)

        # اسم التطبيق
        app_name = tk.Label(logo_frame, text="إدارة الصيانة",
                           font=('Segoe UI', 14, 'bold'),
                           bg=self.colors['dark_secondary'], fg='white')
        app_name.pack()

        app_version = tk.Label(logo_frame, text="الإصدار 2025",
                              font=('Segoe UI', 9),
                              bg=self.colors['dark_secondary'], fg=self.colors['gray'])
        app_version.pack()

        # قائمة التنقل
        self.nav_frame = tk.Frame(self.sidebar, bg=self.colors['dark_secondary'])
        self.nav_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عناصر التنقل
        nav_items = [
            ("🏠", "لوحة التحكم", "dashboard", self.colors['primary']),
            ("👥", "العملاء", "customers", self.colors['accent']),
            ("🔧", "طلبات الصيانة", "orders", self.colors['warning']),
            ("📦", "المخزون", "inventory", self.colors['success']),
            ("📊", "التقارير", "reports", self.colors['secondary']),
            ("⚙️", "الإعدادات", "settings", self.colors['gray']),
        ]

        self.nav_buttons = {}
        for icon, text, key, color in nav_items:
            btn_frame = tk.Frame(self.nav_frame, bg=self.colors['dark_secondary'])
            btn_frame.pack(fill='x', pady=5)

            btn = tk.Button(btn_frame, text=f"{icon}  {text}",
                           font=('Segoe UI', 11, 'bold'),
                           bg=self.colors['dark_secondary'],
                           fg='white',
                           activebackground=color,
                           activeforeground='white',
                           relief='flat',
                           bd=0,
                           padx=20,
                           pady=15,
                           anchor='w',
                           command=lambda k=key: self.show_frame(k))
            btn.pack(fill='x')

            # تأثير hover
            btn.bind('<Enter>', lambda event, b=btn, c=color: self.on_nav_hover(b, c))
            btn.bind('<Leave>', lambda event, b=btn: self.on_nav_leave(b))

            self.nav_buttons[key] = btn

    def create_top_bar(self):
        """إنشاء الشريط العلوي العصري"""
        self.top_bar = tk.Frame(self.main_container, bg=self.colors['dark'], height=70)
        self.top_bar.pack(fill='x', side='top')
        self.top_bar.pack_propagate(False)

        # الجانب الأيسر - العنوان
        left_frame = tk.Frame(self.top_bar, bg=self.colors['dark'])
        left_frame.pack(side='left', fill='y', padx=30, pady=15)

        self.page_title = tk.Label(left_frame, text="لوحة التحكم",
                                  font=('Segoe UI', 18, 'bold'),
                                  bg=self.colors['dark'], fg='white')
        self.page_title.pack(side='left')

        # الجانب الأيمن - أدوات سريعة
        right_frame = tk.Frame(self.top_bar, bg=self.colors['dark'])
        right_frame.pack(side='right', fill='y', padx=30, pady=15)

        # زر البحث العام
        search_btn = tk.Button(right_frame, text="🔍 بحث",
                              font=('Segoe UI', 10, 'bold'),
                              bg=self.colors['primary'],
                              fg='white',
                              relief='flat',
                              padx=15,
                              pady=8,
                              command=self.open_global_search)
        search_btn.pack(side='right', padx=5)

        # زر الإشعارات
        notifications_btn = tk.Button(right_frame, text="🔔 3",
                                     font=('Segoe UI', 10, 'bold'),
                                     bg=self.colors['warning'],
                                     fg='white',
                                     relief='flat',
                                     padx=15,
                                     pady=8,
                                     command=self.show_notifications)
        notifications_btn.pack(side='right', padx=5)

        # معلومات المستخدم
        user_frame = tk.Frame(right_frame, bg=self.colors['dark'])
        user_frame.pack(side='right', padx=20)

        user_avatar = tk.Label(user_frame, text="👤",
                              font=('Segoe UI Emoji', 16),
                              bg=self.colors['dark'], fg=self.colors['primary'])
        user_avatar.pack(side='right')

        user_info = tk.Frame(user_frame, bg=self.colors['dark'])
        user_info.pack(side='right', padx=10)

        user_name = tk.Label(user_info, text="المدير العام",
                            font=('Segoe UI', 10, 'bold'),
                            bg=self.colors['dark'], fg='white')
        user_name.pack(anchor='e')

        user_role = tk.Label(user_info, text="مدير النظام",
                            font=('Segoe UI', 8),
                            bg=self.colors['dark'], fg=self.colors['gray'])
        user_role.pack(anchor='e')

    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        self.main_container = tk.Frame(self, bg=self.colors['dark'])
        self.main_container.pack(side='right', fill='both', expand=True)

        # منطقة المحتوى
        self.content_area = tk.Frame(self.main_container, bg='#f8fafc')
        self.content_area.pack(fill='both', expand=True, padx=0, pady=0)

    def create_status_bar(self):
        """إنشاء شريط الحالة العصري"""
        self.status_bar = tk.Frame(self.main_container, bg=self.colors['dark_secondary'], height=40)
        self.status_bar.pack(fill='x', side='bottom')
        self.status_bar.pack_propagate(False)

        # الحالة
        status_left = tk.Frame(self.status_bar, bg=self.colors['dark_secondary'])
        status_left.pack(side='left', fill='y', padx=20, pady=10)

        self.status_label = tk.Label(status_left, text="🟢 النظام يعمل بشكل طبيعي",
                                    font=('Segoe UI', 9),
                                    bg=self.colors['dark_secondary'], fg=self.colors['success'])
        self.status_label.pack(side='left')

        # الوقت والتاريخ
        status_right = tk.Frame(self.status_bar, bg=self.colors['dark_secondary'])
        status_right.pack(side='right', fill='y', padx=20, pady=10)

        self.time_label = tk.Label(status_right, text="",
                                  font=('Segoe UI', 9, 'bold'),
                                  bg=self.colors['dark_secondary'], fg='white')
        self.time_label.pack(side='right')

    def show_frame(self, frame_name):
        """عرض الإطار المحدد مع تأثيرات انتقال"""
        # تحديث عنوان الصفحة
        titles = {
            "dashboard": "لوحة التحكم",
            "customers": "إدارة العملاء",
            "orders": "طلبات الصيانة",
            "inventory": "إدارة المخزون",
            "reports": "التقارير والإحصائيات",
            "settings": "إعدادات النظام"
        }

        self.page_title.config(text=titles.get(frame_name, "لوحة التحكم"))

        # تحديث أزرار التنقل
        for key, btn in self.nav_buttons.items():
            if key == frame_name:
                btn.config(bg=self.colors['primary'])
            else:
                btn.config(bg=self.colors['dark_secondary'])

        # إخفاء الإطار الحالي
        if self.current_frame:
            self.current_frame.pack_forget()

        # عرض الإطار الجديد
        if frame_name == "dashboard":
            self.show_dashboard()
        elif frame_name == "customers":
            if "customers" not in self.frames:
                self.frames["customers"] = ModernCustomerFrame(self.content_area)
            self.current_frame = self.frames["customers"]
        elif frame_name == "orders":
            if "orders" not in self.frames:
                self.frames["orders"] = ModernOrderFrame(self.content_area)
            self.current_frame = self.frames["orders"]
        elif frame_name == "inventory":
            if "inventory" not in self.frames:
                self.frames["inventory"] = ModernInventoryFrame(self.content_area)
            self.current_frame = self.frames["inventory"]
        elif frame_name == "reports":
            if "reports" not in self.frames:
                self.frames["reports"] = ModernReportFrame(self.content_area)
            self.current_frame = self.frames["reports"]
        elif frame_name == "settings":
            self.show_settings()

        if self.current_frame:
            self.current_frame.pack(fill="both", expand=True)

    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        # إنشاء إطار لوحة التحكم
        if "dashboard" not in self.frames:
            self.frames["dashboard"] = self.create_dashboard_content()

        self.current_frame = self.frames["dashboard"]

    def create_dashboard_content(self):
        """إنشاء محتوى لوحة التحكم العصرية"""
        dashboard = tk.Frame(self.content_area, bg='#f8fafc')

        # منطقة البطاقات العلوية
        cards_frame = tk.Frame(dashboard, bg='#f8fafc')
        cards_frame.pack(fill='x', padx=30, pady=30)

        # بطاقات الإحصائيات
        self.create_stat_cards(cards_frame)

        # منطقة الرسوم البيانية والجداول
        content_frame = tk.Frame(dashboard, bg='#f8fafc')
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 30))

        # الجانب الأيسر - الرسوم البيانية
        left_panel = tk.Frame(content_frame, bg='white', relief='flat', bd=0)
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 15))

        self.create_charts_panel(left_panel)

        # الجانب الأيمن - الأنشطة الحديثة
        right_panel = tk.Frame(content_frame, bg='white', relief='flat', bd=0, width=400)
        right_panel.pack(side='right', fill='y', padx=(15, 0))
        right_panel.pack_propagate(False)

        self.create_activity_panel(right_panel)

        return dashboard

    def create_stat_cards(self, parent):
        """إنشاء بطاقات الإحصائيات العصرية"""
        try:
            conn = get_connection()
            c = conn.cursor()

            # جمع الإحصائيات
            c.execute("SELECT COUNT(*) FROM customers")
            total_customers = c.fetchone()[0]

            c.execute("SELECT COUNT(*) FROM orders WHERE status = 'في الانتظار'")
            pending_orders = c.fetchone()[0]

            c.execute("SELECT SUM(amount) FROM payments WHERE DATE(paid_at) = DATE('now')")
            today_revenue = c.fetchone()[0] or 0

            c.execute("SELECT COUNT(*) FROM inventory WHERE quantity <= low_stock_threshold")
            low_stock_items = c.fetchone()[0]

            conn.close()

            # بيانات البطاقات
            cards_data = [
                ("👥", "العملاء", total_customers, "إجمالي العملاء", self.colors['primary'], "+12%"),
                ("⏳", "طلبات معلقة", pending_orders, "في الانتظار", self.colors['warning'], "+5%"),
                ("💰", "إيرادات اليوم", f"{today_revenue:.0f} ريال", "المبيعات اليومية", self.colors['success'], "+8%"),
                ("⚠️", "تنبيهات المخزون", low_stock_items, "قطع ناقصة", self.colors['danger'], "-2%")
            ]

            for i, (icon, title, value, subtitle, color, change) in enumerate(cards_data):
                card = self.create_modern_card(parent, icon, title, value, subtitle, color, change)
                card.pack(side='left', fill='x', expand=True, padx=10 if i > 0 else 0)

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")

    def create_modern_card(self, parent, icon, title, value, subtitle, color, change):
        """إنشاء بطاقة إحصائية عصرية"""
        card = tk.Frame(parent, bg='white', relief='flat', bd=0)

        # تأثير الظل (محاكاة)
        shadow = tk.Frame(card, bg='#e5e7eb', height=2)
        shadow.pack(fill='x', side='bottom')

        # محتوى البطاقة
        content = tk.Frame(card, bg='white', padx=25, pady=20)
        content.pack(fill='both', expand=True)

        # الصف العلوي - الأيقونة والتغيير
        top_row = tk.Frame(content, bg='white')
        top_row.pack(fill='x')

        # الأيقونة
        icon_label = tk.Label(top_row, text=icon, font=('Segoe UI Emoji', 24),
                             bg='white', fg=color)
        icon_label.pack(side='left')

        # نسبة التغيير
        change_color = self.colors['success'] if change.startswith('+') else self.colors['danger']
        change_label = tk.Label(top_row, text=change, font=('Segoe UI', 10, 'bold'),
                               bg='white', fg=change_color)
        change_label.pack(side='right')

        # القيمة الرئيسية
        value_label = tk.Label(content, text=str(value), font=('Segoe UI', 28, 'bold'),
                              bg='white', fg='#1f2937')
        value_label.pack(anchor='w', pady=(10, 5))

        # العنوان والوصف
        title_label = tk.Label(content, text=title, font=('Segoe UI', 12, 'bold'),
                              bg='white', fg='#374151')
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(content, text=subtitle, font=('Segoe UI', 9),
                                 bg='white', fg='#6b7280')
        subtitle_label.pack(anchor='w')

        return card

    def create_charts_panel(self, parent):
        """إنشاء لوحة الرسوم البيانية"""
        # عنوان اللوحة
        header = tk.Frame(parent, bg='white', padx=25, pady=20)
        header.pack(fill='x')

        title = tk.Label(header, text="📈 تحليل الأداء", font=('Segoe UI', 16, 'bold'),
                        bg='white', fg='#1f2937')
        title.pack(side='left')

        # منطقة الرسم البياني
        chart_area = tk.Frame(parent, bg='white', padx=25, pady=(0, 25))
        chart_area.pack(fill='both', expand=True)

        # رسم بياني مبسط (محاكاة)
        self.create_simple_chart(chart_area)

    def create_simple_chart(self, parent):
        """إنشاء رسم بياني مبسط"""
        canvas = tk.Canvas(parent, bg='white', height=300, highlightthickness=0)
        canvas.pack(fill='both', expand=True)

        # بيانات تجريبية للرسم البياني
        data = [20, 35, 45, 30, 55, 40, 60, 50, 70, 65, 80, 75]
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']

        def draw_chart():
            canvas.delete("all")
            width = canvas.winfo_width()
            height = canvas.winfo_height()

            if width <= 1 or height <= 1:
                canvas.after(100, draw_chart)
                return

            # هوامش الرسم
            margin = 50
            chart_width = width - 2 * margin
            chart_height = height - 2 * margin

            # رسم الخطوط الشبكية
            for i in range(5):
                y = margin + (chart_height * i / 4)
                canvas.create_line(margin, y, width - margin, y,
                                 fill='#f3f4f6', width=1)

            # رسم البيانات
            points = []
            for i, value in enumerate(data):
                x = margin + (chart_width * i / (len(data) - 1))
                y = height - margin - (chart_height * value / 100)
                points.extend([x, y])

                # نقاط البيانات
                canvas.create_oval(x-4, y-4, x+4, y+4,
                                 fill=self.colors['primary'], outline='white', width=2)

            # خط البيانات
            if len(points) >= 4:
                canvas.create_line(points, fill=self.colors['primary'], width=3, smooth=True)

            # تسميات الأشهر
            for i, month in enumerate(months[::2]):  # كل شهرين
                x = margin + (chart_width * (i*2) / (len(data) - 1))
                canvas.create_text(x, height - 20, text=month,
                                 font=('Segoe UI', 9), fill='#6b7280')

        canvas.bind('<Configure>', lambda event: draw_chart())
        canvas.after(100, draw_chart)

    def create_activity_panel(self, parent):
        """إنشاء لوحة الأنشطة الحديثة"""
        # عنوان اللوحة
        header = tk.Frame(parent, bg='white', padx=25, pady=20)
        header.pack(fill='x')

        title = tk.Label(header, text="🕒 الأنشطة الحديثة", font=('Segoe UI', 16, 'bold'),
                        bg='white', fg='#1f2937')
        title.pack(side='left')

        # قائمة الأنشطة
        activities_frame = tk.Frame(parent, bg='white')
        activities_frame.pack(fill='both', expand=True, padx=25, pady=(0, 25))

        # أنشطة تجريبية
        activities = [
            ("👤", "عميل جديد", "تم إضافة أحمد محمد", "منذ 5 دقائق", self.colors['success']),
            ("🔧", "طلب صيانة", "iPhone 13 - شاشة مكسورة", "منذ 15 دقيقة", self.colors['warning']),
            ("💰", "دفعة جديدة", "تم استلام 500 ريال", "منذ 30 دقيقة", self.colors['primary']),
            ("📦", "تحديث المخزون", "إضافة 10 شاشات جديدة", "منذ ساعة", self.colors['accent']),
            ("✅", "طلب مكتمل", "تم إصلاح Samsung Galaxy", "منذ ساعتين", self.colors['success']),
        ]

        for icon, title, desc, time, color in activities:
            self.create_activity_item(activities_frame, icon, title, desc, time, color)

    def create_activity_item(self, parent, icon, title, desc, time, color):
        """إنشاء عنصر نشاط"""
        item = tk.Frame(parent, bg='white', pady=15)
        item.pack(fill='x')

        # الأيقونة
        icon_frame = tk.Frame(item, bg='white')
        icon_frame.pack(side='left', padx=(0, 15))

        icon_bg = tk.Frame(icon_frame, bg=color, width=40, height=40)
        icon_bg.pack()
        icon_bg.pack_propagate(False)

        icon_label = tk.Label(icon_bg, text=icon, font=('Segoe UI Emoji', 16),
                             bg=color, fg='white')
        icon_label.pack(expand=True)

        # المحتوى
        content = tk.Frame(item, bg='white')
        content.pack(side='left', fill='x', expand=True)

        title_label = tk.Label(content, text=title, font=('Segoe UI', 11, 'bold'),
                              bg='white', fg='#1f2937', anchor='w')
        title_label.pack(fill='x')

        desc_label = tk.Label(content, text=desc, font=('Segoe UI', 9),
                             bg='white', fg='#6b7280', anchor='w')
        desc_label.pack(fill='x')

        # الوقت
        time_label = tk.Label(item, text=time, font=('Segoe UI', 8),
                             bg='white', fg='#9ca3af')
        time_label.pack(side='right', padx=(15, 0))

        # خط فاصل
        separator = tk.Frame(parent, bg='#f3f4f6', height=1)
        separator.pack(fill='x', pady=5)

    def on_nav_hover(self, button, color):
        """تأثير hover على أزرار التنقل"""
        button.config(bg=color)

    def on_nav_leave(self, button):
        """إزالة تأثير hover"""
        button.config(bg=self.colors['dark_secondary'])

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now()
        time_str = current_time.strftime("%H:%M:%S")
        date_str = current_time.strftime("%Y-%m-%d")
        self.time_label.config(text=f"📅 {date_str} ⏰ {time_str}")
        self.after(1000, self.update_time)

    def animate_startup(self):
        """تأثير الحركة عند بدء التشغيل"""
        self.attributes('-alpha', 0.0)
        self.fade_in()

    def fade_in(self, alpha=0.0):
        """تأثير الظهور التدريجي"""
        alpha += 0.05
        if alpha >= 1.0:
            alpha = 1.0
        self.attributes('-alpha', alpha)
        if alpha < 1.0:
            self.after(30, lambda: self.fade_in(alpha))

    def open_global_search(self):
        """فتح نافذة البحث العام"""
        search_window = tk.Toplevel(self)
        search_window.title("البحث العام")
        search_window.geometry("600x400")
        search_window.configure(bg='white')
        search_window.transient(self)
        search_window.grab_set()

        # محتوى نافذة البحث
        tk.Label(search_window, text="🔍 البحث العام",
                font=('Segoe UI', 18, 'bold'),
                bg='white', fg=self.colors['primary']).pack(pady=30)

        search_frame = tk.Frame(search_window, bg='white')
        search_frame.pack(pady=20, padx=50, fill='x')

        search_entry = tk.Entry(search_frame, font=('Segoe UI', 12),
                               relief='flat', bd=10)
        search_entry.pack(fill='x', pady=10)
        search_entry.focus()

        tk.Button(search_frame, text="بحث", font=('Segoe UI', 12, 'bold'),
                 bg=self.colors['primary'], fg='white', relief='flat',
                 padx=30, pady=10).pack(pady=20)

    def show_notifications(self):
        """عرض الإشعارات"""
        notifications_window = tk.Toplevel(self)
        notifications_window.title("الإشعارات")
        notifications_window.geometry("400x500")
        notifications_window.configure(bg='white')
        notifications_window.transient(self)
        notifications_window.grab_set()

        # محتوى الإشعارات
        tk.Label(notifications_window, text="🔔 الإشعارات",
                font=('Segoe UI', 18, 'bold'),
                bg='white', fg=self.colors['primary']).pack(pady=20)

        # قائمة الإشعارات
        notifications = [
            "⚠️ 3 قطع غيار تحتاج إعادة تموين",
            "💰 تم استلام دفعة جديدة بقيمة 1500 ريال",
            "🔧 5 طلبات صيانة في الانتظار",
            "👤 عميل جديد تم تسجيله اليوم"
        ]

        for notification in notifications:
            notif_frame = tk.Frame(notifications_window, bg='#f8fafc', relief='flat', bd=1)
            notif_frame.pack(fill='x', padx=20, pady=5)

            tk.Label(notif_frame, text=notification, font=('Segoe UI', 10),
                    bg='#f8fafc', fg='#374151', anchor='w', padx=15, pady=10).pack(fill='x')

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        settings_frame = tk.Frame(self.content_area, bg='#f8fafc')

        # عنوان الإعدادات
        title_frame = tk.Frame(settings_frame, bg='white', padx=30, pady=20)
        title_frame.pack(fill='x', padx=30, pady=30)

        tk.Label(title_frame, text="⚙️ إعدادات النظام",
                font=('Segoe UI', 20, 'bold'),
                bg='white', fg='#1f2937').pack(anchor='w')

        # بطاقات الإعدادات
        settings_content = tk.Frame(settings_frame, bg='#f8fafc')
        settings_content.pack(fill='both', expand=True, padx=30, pady=(0, 30))

        settings_options = [
            ("🎨", "المظهر والثيمات", "تخصيص ألوان وشكل التطبيق"),
            ("🔐", "الأمان والخصوصية", "إدارة كلمات المرور والصلاحيات"),
            ("💾", "النسخ الاحتياطي", "إعدادات النسخ الاحتياطي التلقائي"),
            ("📧", "الإشعارات", "تخصيص الإشعارات والتنبيهات"),
            ("🌐", "اللغة والمنطقة", "تغيير اللغة وإعدادات المنطقة"),
            ("📊", "التقارير", "إعدادات التقارير والتصدير")
        ]

        for i, (icon, title, desc) in enumerate(settings_options):
            if i % 2 == 0:
                row_frame = tk.Frame(settings_content, bg='#f8fafc')
                row_frame.pack(fill='x', pady=10)

            setting_card = tk.Frame(row_frame, bg='white', relief='flat', bd=0, padx=25, pady=20)
            setting_card.pack(side='left', fill='both', expand=True, padx=10 if i % 2 == 1 else (0, 5))

            # الأيقونة والعنوان
            header = tk.Frame(setting_card, bg='white')
            header.pack(fill='x')

            tk.Label(header, text=icon, font=('Segoe UI Emoji', 24),
                    bg='white', fg=self.colors['primary']).pack(side='left')

            tk.Label(header, text=title, font=('Segoe UI', 14, 'bold'),
                    bg='white', fg='#1f2937').pack(side='left', padx=(15, 0))

            # الوصف
            tk.Label(setting_card, text=desc, font=('Segoe UI', 10),
                    bg='white', fg='#6b7280', anchor='w').pack(fill='x', pady=(10, 0))

        self.current_frame = settings_frame
