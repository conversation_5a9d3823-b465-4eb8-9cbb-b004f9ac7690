"""
واجهة التقارير والإحصائيات العصرية 2025
Modern Reports and Analytics UI 2025
"""
import tkinter as tk
from tkinter import ttk, messagebox

class ModernReportFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8fafc')
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6', 
            'accent': '#06b6d4',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'dark': '#1f2937',
            'gray': '#6b7280',
            'light_gray': '#f3f4f6',
            'white': '#ffffff'
        }
        self.create_modern_widgets()

    def create_modern_widgets(self):
        """إنشاء عناصر الواجهة العصرية"""
        # الشريط العلوي
        top_frame = tk.Frame(self, bg='white', padx=30, pady=25)
        top_frame.pack(fill='x', padx=30, pady=(30, 0))
        
        # العنوان الرئيسي
        title_label = tk.Label(top_frame, text="📊 التقارير والإحصائيات", 
                              font=('Segoe UI', 24, 'bold'),
                              bg='white', fg=self.colors['dark'])
        title_label.pack(side='left')
        
        # زر تصدير التقارير
        export_btn = tk.Button(top_frame, text="📤 تصدير التقارير",
                              font=('Segoe UI', 12, 'bold'),
                              bg=self.colors['accent'], fg='white',
                              relief='flat', padx=25, pady=12,
                              command=self.export_reports)
        export_btn.pack(side='right')
        
        # الوصف
        desc_label = tk.Label(top_frame, text="تحليل شامل للأداء والإحصائيات المالية",
                             font=('Segoe UI', 11),
                             bg='white', fg=self.colors['gray'])
        desc_label.pack(anchor='w', pady=(10, 0))
        
        # بطاقات الإحصائيات الرئيسية
        stats_frame = tk.Frame(self, bg='#f8fafc')
        stats_frame.pack(fill='x', padx=30, pady=20)
        
        # بطاقات الأداء
        stats_data = [
            ("💰", "إيرادات الشهر", "15,750 ريال", self.colors['success']),
            ("📈", "نمو المبيعات", "+12.5%", self.colors['primary']),
            ("👥", "عملاء جدد", "23", self.colors['accent']),
            ("⭐", "تقييم الخدمة", "4.8/5", self.colors['warning'])
        ]
        
        for icon, title, value, color in stats_data:
            card = tk.Frame(stats_frame, bg='white', relief='flat', bd=0, padx=20, pady=15)
            card.pack(side='left', fill='x', expand=True, padx=5)
            
            tk.Label(card, text=icon, font=('Segoe UI Emoji', 24),
                    bg='white', fg=color).pack()
            
            tk.Label(card, text=value, font=('Segoe UI', 16, 'bold'),
                    bg='white', fg=self.colors['dark']).pack()
            
            tk.Label(card, text=title, font=('Segoe UI', 11),
                    bg='white', fg=self.colors['gray']).pack()
        
        # منطقة التقارير
        reports_frame = tk.Frame(self, bg='white', padx=30, pady=20)
        reports_frame.pack(fill='both', expand=True, padx=30, pady=(15, 30))
        
        # عنوان التقارير
        tk.Label(reports_frame, text="📋 أنواع التقارير المتاحة", 
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w', pady=(0, 20))
        
        # شبكة التقارير
        reports_grid = tk.Frame(reports_frame, bg='white')
        reports_grid.pack(fill='both', expand=True)
        
        # تقارير مختلفة
        reports_data = [
            ("📊", "تقرير الإيرادات", "تحليل الإيرادات اليومية والشهرية", self.colors['success']),
            ("👥", "تقرير العملاء", "إحصائيات العملاء وأفضل العملاء", self.colors['primary']),
            ("🔧", "تقرير الطلبات", "حالة طلبات الصيانة والإصلاح", self.colors['warning']),
            ("📦", "تقرير المخزون", "حالة المخزون والقطع الناقصة", self.colors['accent']),
            ("💸", "تقرير المصروفات", "تحليل المصروفات والتكاليف", self.colors['danger']),
            ("⚡", "تقرير الأداء", "مؤشرات الأداء الرئيسية", self.colors['secondary'])
        ]
        
        for i, (icon, title, desc, color) in enumerate(reports_data):
            row = i // 2
            col = i % 2
            
            report_card = tk.Frame(reports_grid, bg='#f8fafc', relief='flat', bd=0, padx=20, pady=15)
            report_card.grid(row=row, column=col, sticky='ew', padx=10, pady=10)
            
            # تكوين الشبكة
            reports_grid.grid_columnconfigure(0, weight=1)
            reports_grid.grid_columnconfigure(1, weight=1)
            
            # محتوى البطاقة
            header_frame = tk.Frame(report_card, bg='#f8fafc')
            header_frame.pack(fill='x', pady=(0, 10))
            
            tk.Label(header_frame, text=icon, font=('Segoe UI Emoji', 20),
                    bg='#f8fafc', fg=color).pack(side='left')
            
            tk.Label(header_frame, text=title, font=('Segoe UI', 14, 'bold'),
                    bg='#f8fafc', fg=self.colors['dark']).pack(side='left', padx=(10, 0))
            
            tk.Label(report_card, text=desc, font=('Segoe UI', 10),
                    bg='#f8fafc', fg=self.colors['gray'], wraplength=200).pack(anchor='w')
            
            # زر عرض التقرير
            view_btn = tk.Button(report_card, text="عرض التقرير",
                               font=('Segoe UI', 9, 'bold'),
                               bg=color, fg='white', relief='flat',
                               padx=15, pady=5,
                               command=lambda t=title: self.show_report(t))
            view_btn.pack(anchor='w', pady=(10, 0))

    def show_report(self, report_type):
        """عرض التقرير المحدد"""
        messagebox.showinfo("تقرير", f"عرض {report_type} قيد التطوير")

    def export_reports(self):
        """تصدير التقارير"""
        messagebox.showinfo("تصدير", "ميزة تصدير التقارير قيد التطوير")
