#!/usr/bin/env python3
"""
ملف تشغيل الواجهة العصرية 2025
Modern UI 2025 Launcher
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """فحص المتطلبات الأساسية"""
    required_modules = ['tkinter', 'sqlite3', 'datetime', 're']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"المتطلبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    return True

def setup_paths():
    """إعداد المسارات"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # إضافة المجلدات للمسار
    paths_to_add = [
        current_dir,
        os.path.join(current_dir, 'ui'),
        os.path.join(current_dir, 'database'),
        os.path.join(current_dir, 'models'),
        os.path.join(current_dir, 'utils')
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.append(path)

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        from database.db_manager import get_connection
        
        conn = get_connection()
        c = conn.cursor()
        
        # فحص وجود بيانات
        c.execute("SELECT COUNT(*) FROM customers")
        customer_count = c.fetchone()[0]
        
        if customer_count == 0:
            # إضافة عملاء تجريبيين
            sample_customers = [
                ("أحمد محمد العلي", "0501234567", "<EMAIL>", "الرياض، حي النخيل، شارع الملك فهد"),
                ("فاطمة سعد الأحمد", "0509876543", "<EMAIL>", "جدة، حي الصفا، طريق الملك عبدالعزيز"),
                ("محمد عبدالله السعد", "0551234567", "<EMAIL>", "الدمام، حي الفيصلية، شارع الأمير محمد"),
                ("نورا خالد المحمد", "0561234567", "<EMAIL>", "مكة المكرمة، حي العزيزية، طريق الحرم"),
                ("خالد عبدالرحمن القحطاني", "0571234567", "<EMAIL>", "المدينة المنورة، حي قباء، شارع النور"),
                ("سارة أحمد الزهراني", "0581234567", "<EMAIL>", "الطائف، حي الشفا، طريق الهدا"),
                ("عبدالله محمد الغامدي", "0591234567", "<EMAIL>", "أبها، حي النسيم، شارع الملك خالد")
            ]
            
            for customer in sample_customers:
                c.execute("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)", customer)
            
            # إضافة قطع غيار تجريبية
            sample_inventory = [
                ("شاشة iPhone 14 Pro", "IP14P-SCR-001", 15, 650.00, 3),
                ("بطارية Samsung Galaxy S23", "SG23-BAT-001", 20, 180.00, 5),
                ("كاميرا خلفية iPhone 13", "IP13-CAM-001", 12, 320.00, 2),
                ("شاحن سريع USB-C 65W", "CHG-TC-65W", 30, 85.00, 10),
                ("واقي شاشة زجاجي مقوى", "SCR-PROT-GLASS", 100, 25.00, 20),
                ("سماعة داخلية iPhone 14", "IP14-SPK-001", 18, 120.00, 4),
                ("فليكس الشحن Samsung S22", "SG22-FLX-001", 25, 65.00, 8),
                ("كاميرا أمامية Huawei P50", "HW-P50-FCAM", 8, 150.00, 3),
                ("شاشة Xiaomi Mi 11", "XM11-SCR-001", 10, 280.00, 2),
                ("بطارية iPhone 12 Pro Max", "IP12PM-BAT-001", 14, 220.00, 3),
                ("مكبر صوت خارجي Samsung", "SG-SPK-EXT-001", 22, 95.00, 6),
                ("كابل Lightning أصلي", "CABLE-LIGHT-ORI", 50, 45.00, 15)
            ]
            
            for item in sample_inventory:
                c.execute("INSERT INTO inventory (name, part_number, quantity, price, low_stock_threshold) VALUES (?, ?, ?, ?, ?)", item)
            
            # إضافة طلبات صيانة تجريبية
            sample_orders = [
                (1, "Apple", "iPhone 14 Pro", "شاشة مكسورة مع خطوط ملونة", 700.00, "في الانتظار", "أحمد الفني", "2024-01-20 10:30:00"),
                (2, "Samsung", "Galaxy S23", "بطارية لا تشحن ولا تحتفظ بالشحن", 200.00, "قيد الإصلاح", "محمد الفني", "2024-01-19 14:20:00"),
                (3, "Apple", "iPhone 13", "كاميرا خلفية لا تعمل", 350.00, "مكتمل", "أحمد الفني", "2024-01-18 09:15:00"),
                (4, "Huawei", "P50 Pro", "مشكلة في الصوت والمكالمات", 220.00, "تم التسليم", "سعد الفني", "2024-01-17 16:45:00"),
                (5, "Samsung", "Galaxy Note 20", "شاشة سوداء لا تستجيب", 480.00, "في الانتظار", "محمد الفني", "2024-01-21 11:00:00"),
                (6, "Xiaomi", "Mi 11", "مشكلة في الشحن", 320.00, "قيد الإصلاح", "علي الفني", "2024-01-20 13:30:00"),
                (7, "Apple", "iPhone 12 Pro Max", "بطارية تنفد بسرعة", 250.00, "مكتمل", "أحمد الفني", "2024-01-16 08:20:00")
            ]
            
            for order in sample_orders:
                c.execute("INSERT INTO orders (customer_id, device_brand, device_model, issue, estimated_cost, status, technician, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", order)
            
            # إضافة مدفوعات تجريبية
            sample_payments = [
                (3, 350.00, "2024-01-18 17:30:00"),
                (4, 220.00, "2024-01-17 18:00:00"),
                (7, 250.00, "2024-01-16 19:15:00"),
                (1, 350.00, "2024-01-20 12:00:00"),  # دفعة جزئية
                (2, 100.00, "2024-01-19 15:30:00")   # دفعة جزئية
            ]
            
            for payment in sample_payments:
                c.execute("INSERT INTO payments (order_id, amount, paid_at) VALUES (?, ?, ?)", payment)
            
            # إضافة مصروفات تجريبية
            sample_expenses = [
                ("2024-01-15", "شراء قطع غيار من المورد الرئيسي", 2500.00),
                ("2024-01-12", "فاتورة كهرباء المحل", 380.00),
                ("2024-01-10", "أدوات صيانة جديدة", 650.00),
                ("2024-01-08", "إيجار المحل - شهر يناير", 3000.00),
                ("2024-01-05", "راتب الفني أحمد", 4000.00),
                ("2024-01-05", "راتب الفني محمد", 3800.00),
                ("2024-01-03", "مصاريف تسويق وإعلان", 800.00)
            ]
            
            for expense in sample_expenses:
                c.execute("INSERT INTO expenses (date, description, amount) VALUES (?, ?, ?)", expense)
            
            conn.commit()
            print("✅ تم إنشاء البيانات التجريبية بنجاح")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")

def show_startup_splash():
    """عرض شاشة البداية العصرية"""
    splash = tk.Tk()
    splash.title("نظام إدارة محل صيانة الجوالات 2025")
    splash.geometry("600x400")
    splash.configure(bg='#0f0f23')
    splash.resizable(False, False)
    
    # توسيط النافذة
    splash.update_idletasks()
    x = (splash.winfo_screenwidth() // 2) - (300)
    y = (splash.winfo_screenheight() // 2) - (200)
    splash.geometry(f'600x400+{x}+{y}')
    
    # إزالة شريط العنوان
    splash.overrideredirect(True)
    
    # محتوى شاشة البداية
    tk.Label(splash, text="📱", font=('Segoe UI Emoji', 64),
            bg='#0f0f23', fg='#6366f1').pack(pady=50)
    
    tk.Label(splash, text="نظام إدارة محل صيانة الجوالات",
            font=('Segoe UI', 24, 'bold'),
            bg='#0f0f23', fg='white').pack(pady=10)
    
    tk.Label(splash, text="الإصدار العصري 2025",
            font=('Segoe UI', 14),
            bg='#0f0f23', fg='#8b5cf6').pack()
    
    tk.Label(splash, text="جاري التحميل...",
            font=('Segoe UI', 12),
            bg='#0f0f23', fg='#6b7280').pack(pady=30)
    
    # شريط التقدم
    progress_frame = tk.Frame(splash, bg='#0f0f23')
    progress_frame.pack(pady=20)
    
    progress_bg = tk.Frame(progress_frame, bg='#1e1e3f', width=300, height=6)
    progress_bg.pack()
    
    progress_bar = tk.Frame(progress_bg, bg='#6366f1', width=0, height=6)
    progress_bar.place(x=0, y=0)
    
    # تحريك شريط التقدم
    def animate_progress():
        for i in range(301):
            progress_bar.config(width=i)
            splash.update()
            splash.after(5)
    
    splash.after(500, animate_progress)
    splash.after(2000, splash.destroy)
    splash.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🚀 " + "=" * 60)
    print("🚀 نظام إدارة محل صيانة الجوالات - الواجهة العصرية 2025")
    print("🚀 Phone Repair Shop Management System - Modern UI 2025")
    print("🚀 " + "=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ يرجى تثبيت المتطلبات المفقودة أولاً")
        return
    
    # إعداد المسارات
    setup_paths()
    
    try:
        # عرض شاشة البداية
        print("🎨 عرض شاشة البداية العصرية...")
        show_startup_splash()
        
        # تهيئة قاعدة البيانات
        print("🗄️ تهيئة قاعدة البيانات...")
        from database.db_manager import init_db
        init_db()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # إنشاء بيانات تجريبية
        print("📊 إنشاء البيانات التجريبية...")
        create_sample_data()
        
        # تشغيل التطبيق العصري
        print("🎨 تشغيل الواجهة العصرية...")
        from ui.modern_dashboard import ModernDashboardApp
        
        app = ModernDashboardApp()
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎉 استمتع بالواجهة العصرية الجديدة!")
        app.mainloop()
        
    except ImportError as e:
        error_msg = f"❌ خطأ في استيراد الوحدات: {e}"
        print(error_msg)
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        
    except Exception as e:
        error_msg = f"❌ حدث خطأ أثناء تشغيل التطبيق: {e}"
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
