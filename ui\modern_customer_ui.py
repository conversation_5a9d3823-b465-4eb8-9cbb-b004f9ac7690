"""
واجهة إدارة العملاء العصرية 2025
Modern Customer Management UI 2025
"""
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime
import re

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from database.db_manager import get_connection

class ModernCustomerFrame(tk.Frame):
    def __init__(self, master):
        super().__init__(master)
        self.configure(bg='#f8fafc')
        self.colors = {
            'primary': '#6366f1',
            'secondary': '#8b5cf6',
            'accent': '#06b6d4',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'dark': '#1f2937',
            'gray': '#6b7280',
            'light_gray': '#f3f4f6',
            'white': '#ffffff'
        }
        self.create_modern_widgets()
        self.load_customers()

    def create_modern_widgets(self):
        """إنشاء عناصر الواجهة العصرية"""
        # الشريط العلوي
        self.create_top_section()

        # منطقة البحث والفلاتر
        self.create_search_section()

        # جدول العملاء
        self.create_customers_table()

        # شريط الأدوات السفلي
        self.create_bottom_toolbar()

    def create_top_section(self):
        """إنشاء القسم العلوي"""
        top_frame = tk.Frame(self, bg='white', padx=30, pady=25)
        top_frame.pack(fill='x', padx=30, pady=(30, 0))

        # العنوان والإحصائيات
        title_frame = tk.Frame(top_frame, bg='white')
        title_frame.pack(fill='x')

        # العنوان الرئيسي
        title_label = tk.Label(title_frame, text="👥 إدارة العملاء",
                              font=('Segoe UI', 24, 'bold'),
                              bg='white', fg=self.colors['dark'])
        title_label.pack(side='left')

        # زر إضافة عميل جديد
        add_btn = tk.Button(title_frame, text="➕ عميل جديد",
                           font=('Segoe UI', 12, 'bold'),
                           bg=self.colors['primary'], fg='white',
                           relief='flat', padx=25, pady=12,
                           command=self.add_customer_dialog)
        add_btn.pack(side='right')

        # الوصف
        desc_label = tk.Label(top_frame, text="إدارة شاملة لبيانات العملاء ومتابعة طلباتهم",
                             font=('Segoe UI', 11),
                             bg='white', fg=self.colors['gray'])
        desc_label.pack(anchor='w', pady=(10, 0))

    def create_search_section(self):
        """إنشاء قسم البحث والفلاتر"""
        search_frame = tk.Frame(self, bg='white', padx=30, pady=20)
        search_frame.pack(fill='x', padx=30, pady=(15, 0))

        # شريط البحث
        search_container = tk.Frame(search_frame, bg='white')
        search_container.pack(fill='x')

        # أيقونة البحث
        search_icon = tk.Label(search_container, text="🔍",
                              font=('Segoe UI Emoji', 14),
                              bg='white', fg=self.colors['gray'])
        search_icon.pack(side='left', padx=(0, 10))

        # حقل البحث
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_container, textvariable=self.search_var,
                               font=('Segoe UI', 12), relief='flat',
                               bg='#f8fafc', fg=self.colors['dark'],
                               bd=10, width=40)
        search_entry.pack(side='left', fill='x', expand=True)
        search_entry.bind('<KeyRelease>', lambda e: self.search_customers())

        # أزرار الفلترة
        filter_frame = tk.Frame(search_container, bg='white')
        filter_frame.pack(side='right', padx=(20, 0))

        # زر التصدير
        export_btn = tk.Button(filter_frame, text="📤 تصدير",
                              font=('Segoe UI', 10, 'bold'),
                              bg=self.colors['accent'], fg='white',
                              relief='flat', padx=15, pady=8,
                              command=self.export_customers)
        export_btn.pack(side='right', padx=5)

        # زر الطباعة
        print_btn = tk.Button(filter_frame, text="🖨️ طباعة",
                             font=('Segoe UI', 10, 'bold'),
                             bg=self.colors['secondary'], fg='white',
                             relief='flat', padx=15, pady=8,
                             command=self.print_customers_list)
        print_btn.pack(side='right', padx=5)

    def create_customers_table(self):
        """إنشاء جدول العملاء العصري"""
        table_frame = tk.Frame(self, bg='white', padx=30, pady=20)
        table_frame.pack(fill='both', expand=True, padx=30, pady=(15, 0))

        # عنوان الجدول
        table_header = tk.Frame(table_frame, bg='white')
        table_header.pack(fill='x', pady=(0, 15))

        tk.Label(table_header, text="📋 قائمة العملاء",
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(side='left')

        # إحصائيات سريعة
        self.stats_label = tk.Label(table_header, text="إجمالي العملاء: 0",
                                   font=('Segoe UI', 11),
                                   bg='white', fg=self.colors['gray'])
        self.stats_label.pack(side='right')

        # إطار الجدول مع التمرير
        table_container = tk.Frame(table_frame, bg='white')
        table_container.pack(fill='both', expand=True)

        # إعداد الجدول
        columns = ("id", "name", "phone", "email", "orders_count", "last_order", "status")
        self.tree = ttk.Treeview(table_container, columns=columns, show="headings", height=15)

        # تخصيص أنماط الجدول
        style = ttk.Style()
        style.configure("Treeview",
                       background='white',
                       foreground=self.colors['dark'],
                       fieldbackground='white',
                       font=('Segoe UI', 10))
        style.configure("Treeview.Heading",
                       background=self.colors['light_gray'],
                       foreground=self.colors['dark'],
                       font=('Segoe UI', 11, 'bold'))

        # تعيين عناوين الأعمدة
        headers = {
            "id": "الرقم",
            "name": "اسم العميل",
            "phone": "رقم الهاتف",
            "email": "البريد الإلكتروني",
            "orders_count": "عدد الطلبات",
            "last_order": "آخر طلب",
            "status": "الحالة"
        }

        for col in columns:
            self.tree.heading(col, text=headers[col])
            if col == "id":
                self.tree.column(col, width=80, anchor='center')
            elif col == "name":
                self.tree.column(col, width=180)
            elif col == "phone":
                self.tree.column(col, width=130, anchor='center')
            elif col == "email":
                self.tree.column(col, width=200)
            elif col == "orders_count":
                self.tree.column(col, width=100, anchor='center')
            elif col == "last_order":
                self.tree.column(col, width=120, anchor='center')
            elif col == "status":
                self.tree.column(col, width=100, anchor='center')

        # شريط التمرير العصري
        scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخصيص شريط التمرير
        style.configure("Vertical.TScrollbar",
                       background=self.colors['light_gray'],
                       troughcolor='white',
                       arrowcolor=self.colors['gray'])

        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط الأحداث
        self.tree.bind('<Double-1>', lambda e: self.view_customer_details())
        self.tree.bind('<Button-3>', self.show_context_menu)

    def create_bottom_toolbar(self):
        """إنشاء شريط الأدوات السفلي"""
        toolbar = tk.Frame(self, bg='white', padx=30, pady=20)
        toolbar.pack(fill='x', padx=30, pady=(15, 30))

        # الجانب الأيسر - أزرار العمليات
        left_buttons = tk.Frame(toolbar, bg='white')
        left_buttons.pack(side='left')

        buttons = [
            ("👁️ عرض التفاصيل", self.view_customer_details, self.colors['primary']),
            ("✏️ تعديل", self.edit_customer_dialog, self.colors['warning']),
            ("🗑️ حذف", self.delete_customer, self.colors['danger']),
            ("📊 سجل الطلبات", self.view_customer_history, self.colors['accent'])
        ]

        for text, command, color in buttons:
            btn = tk.Button(left_buttons, text=text, command=command,
                           font=('Segoe UI', 10, 'bold'),
                           bg=color, fg='white', relief='flat',
                           padx=15, pady=8)
            btn.pack(side='left', padx=5)

        # الجانب الأيمن - معلومات إضافية
        right_info = tk.Frame(toolbar, bg='white')
        right_info.pack(side='right')

        self.selection_label = tk.Label(right_info, text="لم يتم تحديد عميل",
                                       font=('Segoe UI', 10),
                                       bg='white', fg=self.colors['gray'])
        self.selection_label.pack()

    def load_customers(self, search=None):
        """تحميل بيانات العملاء مع تحسينات عصرية"""
        # مسح البيانات الحالية
        for row in self.tree.get_children():
            self.tree.delete(row)

        try:
            conn = get_connection()
            c = conn.cursor()

            # استعلام محسن مع إحصائيات
            query = """
                SELECT c.id, c.name, c.phone, c.email,
                       COUNT(o.id) as orders_count,
                       MAX(o.created_at) as last_order
                FROM customers c
                LEFT JOIN orders o ON c.id = o.customer_id
            """

            params = []
            if search:
                query += " WHERE c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ?"
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            query += " GROUP BY c.id ORDER BY c.name"

            c.execute(query, params)
            customers = c.fetchall()

            # إدراج البيانات مع تلوين الصفوف
            for i, row in enumerate(customers):
                customer_id, name, phone, email, orders_count, last_order = row

                # تحديد حالة العميل
                if orders_count == 0:
                    status = "جديد"
                    status_color = self.colors['warning']
                elif orders_count >= 5:
                    status = "VIP"
                    status_color = self.colors['success']
                else:
                    status = "نشط"
                    status_color = self.colors['primary']

                # تنسيق تاريخ آخر طلب
                if last_order:
                    try:
                        last_order_date = datetime.strptime(last_order, '%Y-%m-%d %H:%M:%S')
                        last_order_formatted = last_order_date.strftime('%Y-%m-%d')
                    except:
                        last_order_formatted = last_order
                else:
                    last_order_formatted = "لا يوجد"

                # إدراج البيانات
                item = self.tree.insert('', 'end', values=(
                    customer_id, name, phone, email, orders_count,
                    last_order_formatted, status
                ))

                # تلوين الصفوف بالتناوب
                if i % 2 == 0:
                    self.tree.set(item, "status", status)

            # تحديث الإحصائيات
            self.stats_label.config(text=f"إجمالي العملاء: {len(customers)}")

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات:\n{str(e)}")

    def search_customers(self):
        """البحث في العملاء"""
        search_term = self.search_var.get().strip()
        self.load_customers(search_term if search_term else None)

    def add_customer_dialog(self):
        """نافذة إضافة عميل جديد بتصميم عصري"""
        self.customer_dialog()

    def edit_customer_dialog(self):
        """نافذة تعديل العميل"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للتعديل.")
            return
        values = self.tree.item(selected[0], 'values')
        self.customer_dialog(values)

    def customer_dialog(self, values=None):
        """نافذة إضافة/تعديل العميل بتصميم عصري"""
        dialog = tk.Toplevel(self)
        dialog.title("عميل جديد" if not values else "تعديل العميل")
        dialog.geometry("550x600")
        dialog.configure(bg='white')
        dialog.resizable(False, False)

        # توسيط النافذة
        dialog.transient(self)
        dialog.grab_set()

        # العنوان العصري
        header = tk.Frame(dialog, bg=self.colors['primary'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)

        title_text = "➕ إضافة عميل جديد" if not values else "✏️ تعديل بيانات العميل"
        title_label = tk.Label(header, text=title_text,
                              font=('Segoe UI', 18, 'bold'),
                              bg=self.colors['primary'], fg='white')
        title_label.pack(expand=True)

        # محتوى النافذة
        content = tk.Frame(dialog, bg='white', padx=40, pady=30)
        content.pack(fill='both', expand=True)

        # الحقول
        fields = [
            ("الاسم الكامل", "name", "👤"),
            ("رقم الهاتف", "phone", "📱"),
            ("البريد الإلكتروني", "email", "📧"),
            ("العنوان", "address", "📍")
        ]

        self.field_vars = {}

        for i, (label_text, field_name, icon) in enumerate(fields):
            # إطار الحقل
            field_frame = tk.Frame(content, bg='white')
            field_frame.pack(fill='x', pady=15)

            # التسمية مع الأيقونة
            label_frame = tk.Frame(field_frame, bg='white')
            label_frame.pack(fill='x', pady=(0, 8))

            tk.Label(label_frame, text=f"{icon} {label_text}",
                    font=('Segoe UI', 12, 'bold'),
                    bg='white', fg=self.colors['dark']).pack(side='left')

            # الحقل
            if field_name == "address":
                field_widget = tk.Text(field_frame, height=4, font=('Segoe UI', 11),
                                      relief='flat', bg='#f8fafc', fg=self.colors['dark'],
                                      bd=10, wrap='word')
                if values and len(values) > 4:
                    field_widget.insert('1.0', values[4] if field_name == "address" else "")
            else:
                var = tk.StringVar()
                if values:
                    field_index = {"name": 1, "phone": 2, "email": 3}.get(field_name, 0)
                    if field_index < len(values):
                        var.set(values[field_index])

                field_widget = tk.Entry(field_frame, textvariable=var,
                                       font=('Segoe UI', 12), relief='flat',
                                       bg='#f8fafc', fg=self.colors['dark'],
                                       bd=10)
                self.field_vars[field_name] = var

            field_widget.pack(fill='x')
            if field_name == "address":
                self.field_vars[field_name] = field_widget

        # أزرار العمليات
        buttons_frame = tk.Frame(content, bg='white')
        buttons_frame.pack(pady=30)

        def save_customer():
            """حفظ بيانات العميل"""
            try:
                # جمع البيانات
                name = self.field_vars["name"].get().strip()
                phone = self.field_vars["phone"].get().strip()
                email = self.field_vars["email"].get().strip()

                if isinstance(self.field_vars["address"], tk.Text):
                    address = self.field_vars["address"].get('1.0', 'end-1c').strip()
                else:
                    address = self.field_vars["address"].get().strip()

                # التحقق من صحة البيانات
                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                    return

                if not phone:
                    messagebox.showerror("خطأ", "يرجى إدخال رقم الهاتف")
                    return

                # التحقق من صحة رقم الهاتف
                if not re.match(r'^[\d\s\-\+\(\)]+$', phone):
                    messagebox.showerror("خطأ", "رقم الهاتف غير صحيح")
                    return

                # التحقق من صحة البريد الإلكتروني
                if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                    messagebox.showerror("خطأ", "البريد الإلكتروني غير صحيح")
                    return

                # حفظ في قاعدة البيانات
                conn = get_connection()
                c = conn.cursor()

                if values:  # تعديل
                    c.execute("""UPDATE customers
                               SET name=?, phone=?, email=?, address=?
                               WHERE id=?""",
                             (name, phone, email, address, values[0]))
                    messagebox.showinfo("نجح", "تم تحديث بيانات العميل بنجاح")
                else:  # إضافة جديد
                    c.execute("""INSERT INTO customers (name, phone, email, address)
                               VALUES (?, ?, ?, ?)""",
                             (name, phone, email, address))
                    messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")

                conn.commit()
                conn.close()

                # تحديث الجدول وإغلاق النافذة
                self.load_customers()
                dialog.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حفظ البيانات:\n{str(e)}")

        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_customer,
                            font=('Segoe UI', 12, 'bold'),
                            bg=self.colors['success'], fg='white',
                            relief='flat', padx=30, pady=12)
        save_btn.pack(side='left', padx=10)

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              font=('Segoe UI', 12, 'bold'),
                              bg=self.colors['gray'], fg='white',
                              relief='flat', padx=30, pady=12)
        cancel_btn.pack(side='left', padx=10)

    def delete_customer(self):
        """حذف العميل المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل للحذف.")
            return

        values = self.tree.item(selected[0], 'values')
        customer_name = values[1]

        # نافذة تأكيد عصرية
        confirm_dialog = tk.Toplevel(self)
        confirm_dialog.title("تأكيد الحذف")
        confirm_dialog.geometry("400x250")
        confirm_dialog.configure(bg='white')
        confirm_dialog.resizable(False, False)
        confirm_dialog.transient(self)
        confirm_dialog.grab_set()

        # أيقونة التحذير
        tk.Label(confirm_dialog, text="⚠️", font=('Segoe UI Emoji', 48),
                bg='white', fg=self.colors['warning']).pack(pady=20)

        # رسالة التأكيد
        tk.Label(confirm_dialog, text="هل أنت متأكد من حذف العميل؟",
                font=('Segoe UI', 14, 'bold'),
                bg='white', fg=self.colors['dark']).pack(pady=10)

        tk.Label(confirm_dialog, text=f"العميل: {customer_name}",
                font=('Segoe UI', 12),
                bg='white', fg=self.colors['gray']).pack()

        tk.Label(confirm_dialog, text="سيتم حذف جميع طلبات الصيانة المرتبطة به",
                font=('Segoe UI', 10),
                bg='white', fg=self.colors['danger']).pack(pady=10)

        # أزرار التأكيد
        buttons_frame = tk.Frame(confirm_dialog, bg='white')
        buttons_frame.pack(pady=20)

        def confirm_delete():
            try:
                conn = get_connection()
                c = conn.cursor()

                # حذف طلبات الصيانة المرتبطة أولاً
                c.execute("DELETE FROM orders WHERE customer_id=?", (values[0],))

                # حذف العميل
                c.execute("DELETE FROM customers WHERE id=?", (values[0],))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم حذف العميل '{customer_name}' بنجاح")
                self.load_customers()
                confirm_dialog.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف العميل:\n{str(e)}")

        tk.Button(buttons_frame, text="🗑️ حذف", command=confirm_delete,
                 font=('Segoe UI', 11, 'bold'),
                 bg=self.colors['danger'], fg='white',
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

        tk.Button(buttons_frame, text="❌ إلغاء", command=confirm_dialog.destroy,
                 font=('Segoe UI', 11, 'bold'),
                 bg=self.colors['gray'], fg='white',
                 relief='flat', padx=20, pady=8).pack(side='left', padx=10)

    def view_customer_details(self):
        """عرض تفاصيل العميل"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل لعرض تفاصيله.")
            return

        values = self.tree.item(selected[0], 'values')
        customer_id = values[0]
        customer_name = values[1]

        # نافذة التفاصيل العصرية
        details_window = tk.Toplevel(self)
        details_window.title(f"تفاصيل العميل - {customer_name}")
        details_window.geometry("700x600")
        details_window.configure(bg='#f8fafc')
        details_window.transient(self)
        details_window.grab_set()

        # الشريط العلوي
        header = tk.Frame(details_window, bg=self.colors['primary'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(header, text=f"👤 {customer_name}",
                font=('Segoe UI', 20, 'bold'),
                bg=self.colors['primary'], fg='white').pack(expand=True)

        # محتوى التفاصيل
        content = tk.Frame(details_window, bg='#f8fafc', padx=30, pady=30)
        content.pack(fill='both', expand=True)

        # بطاقة المعلومات الأساسية
        info_card = tk.Frame(content, bg='white', relief='flat', bd=0, padx=25, pady=20)
        info_card.pack(fill='x', pady=(0, 20))

        tk.Label(info_card, text="📋 المعلومات الأساسية",
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w', pady=(0, 15))

        # عرض البيانات
        info_data = [
            ("الرقم", values[0]),
            ("الاسم", values[1]),
            ("الهاتف", values[2]),
            ("البريد الإلكتروني", values[3]),
            ("عدد الطلبات", values[4]),
            ("آخر طلب", values[5])
        ]

        for label, value in info_data:
            row = tk.Frame(info_card, bg='white')
            row.pack(fill='x', pady=5)

            tk.Label(row, text=f"{label}:", font=('Segoe UI', 11, 'bold'),
                    bg='white', fg=self.colors['gray']).pack(side='left')

            tk.Label(row, text=str(value), font=('Segoe UI', 11),
                    bg='white', fg=self.colors['dark']).pack(side='left', padx=(10, 0))

        # بطاقة الطلبات الحديثة
        orders_card = tk.Frame(content, bg='white', relief='flat', bd=0, padx=25, pady=20)
        orders_card.pack(fill='both', expand=True)

        tk.Label(orders_card, text="🔧 الطلبات الحديثة",
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w', pady=(0, 15))

        # جدول الطلبات
        orders_frame = tk.Frame(orders_card, bg='white')
        orders_frame.pack(fill='both', expand=True)

        try:
            conn = get_connection()
            c = conn.cursor()
            c.execute("""SELECT device_brand, device_model, issue, status, created_at
                        FROM orders WHERE customer_id=?
                        ORDER BY created_at DESC LIMIT 5""", (customer_id,))
            orders = c.fetchall()
            conn.close()

            if orders:
                for order in orders:
                    order_row = tk.Frame(orders_frame, bg='#f8fafc', padx=15, pady=10)
                    order_row.pack(fill='x', pady=2)

                    device_text = f"{order[0]} {order[1]}"
                    tk.Label(order_row, text=device_text, font=('Segoe UI', 11, 'bold'),
                            bg='#f8fafc', fg=self.colors['dark']).pack(anchor='w')

                    tk.Label(order_row, text=order[2], font=('Segoe UI', 10),
                            bg='#f8fafc', fg=self.colors['gray']).pack(anchor='w')

                    status_color = {
                        'في الانتظار': self.colors['warning'],
                        'قيد الإصلاح': self.colors['primary'],
                        'مكتمل': self.colors['success'],
                        'تم التسليم': self.colors['success']
                    }.get(order[3], self.colors['gray'])

                    tk.Label(order_row, text=f"الحالة: {order[3]}", font=('Segoe UI', 9, 'bold'),
                            bg='#f8fafc', fg=status_color).pack(anchor='w')
            else:
                tk.Label(orders_frame, text="لا توجد طلبات صيانة لهذا العميل",
                        font=('Segoe UI', 12),
                        bg='white', fg=self.colors['gray']).pack(pady=20)

        except Exception as e:
            tk.Label(orders_frame, text=f"خطأ في تحميل الطلبات: {e}",
                    font=('Segoe UI', 10),
                    bg='white', fg=self.colors['danger']).pack(pady=20)

    def view_customer_history(self):
        """عرض سجل العميل الكامل"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحديد", "يرجى تحديد عميل لعرض سجله.")
            return

        values = self.tree.item(selected[0], 'values')
        customer_name = values[1]
        customer_id = values[0]

        # نافذة السجل
        history_window = tk.Toplevel(self)
        history_window.title(f"سجل العميل - {customer_name}")
        history_window.geometry("900x600")
        history_window.configure(bg='#f8fafc')
        history_window.transient(self)
        history_window.grab_set()

        # الشريط العلوي
        header = tk.Frame(history_window, bg=self.colors['accent'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(header, text=f"📊 سجل العميل: {customer_name}",
                font=('Segoe UI', 18, 'bold'),
                bg=self.colors['accent'], fg='white').pack(expand=True)

        # محتوى السجل
        content = tk.Frame(history_window, bg='#f8fafc', padx=30, pady=30)
        content.pack(fill='both', expand=True)

        # جدول السجل
        history_frame = tk.Frame(content, bg='white', padx=20, pady=20)
        history_frame.pack(fill='both', expand=True)

        # عنوان الجدول
        tk.Label(history_frame, text="📋 جميع طلبات الصيانة",
                font=('Segoe UI', 16, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w', pady=(0, 15))

        # إعداد جدول السجل
        columns = ("order_id", "device", "issue", "status", "cost", "date")
        history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=15)

        headers = {
            "order_id": "رقم الطلب",
            "device": "الجهاز",
            "issue": "المشكلة",
            "status": "الحالة",
            "cost": "التكلفة",
            "date": "التاريخ"
        }

        for col in columns:
            history_tree.heading(col, text=headers[col])
            history_tree.column(col, width=140)

        # شريط التمرير
        history_scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=history_tree.yview)
        history_tree.configure(yscrollcommand=history_scrollbar.set)

        history_tree.pack(side="left", fill="both", expand=True)
        history_scrollbar.pack(side="right", fill="y")

        # تحميل البيانات
        try:
            conn = get_connection()
            c = conn.cursor()
            c.execute("""SELECT id, device_brand || ' ' || device_model, issue, status,
                        estimated_cost, created_at
                        FROM orders WHERE customer_id=? ORDER BY created_at DESC""",
                     (customer_id,))

            orders = c.fetchall()
            for row in orders:
                history_tree.insert('', 'end', values=row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل السجل:\n{str(e)}")

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        # إنشاء القائمة السياقية
        context_menu = tk.Menu(self, tearoff=0)
        context_menu.add_command(label="👁️ عرض التفاصيل", command=self.view_customer_details)
        context_menu.add_command(label="✏️ تعديل", command=self.edit_customer_dialog)
        context_menu.add_separator()
        context_menu.add_command(label="📊 سجل الطلبات", command=self.view_customer_history)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_customer)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def export_customers(self):
        """تصدير قائمة العملاء"""
        messagebox.showinfo("تصدير", "ميزة التصدير قيد التطوير\nسيتم إضافتها في التحديث القادم")

    def print_customers_list(self):
        """طباعة قائمة العملاء"""
        messagebox.showinfo("طباعة", "ميزة الطباعة قيد التطوير\nسيتم إضافتها في التحديث القادم")
