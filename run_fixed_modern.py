#!/usr/bin/env python3
"""
ملف تشغيل الواجهة العصرية المصححة 2025
Fixed Modern UI 2025 Launcher
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk

def setup_paths():
    """إعداد المسارات"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    paths_to_add = [
        current_dir,
        os.path.join(current_dir, 'ui'),
        os.path.join(current_dir, 'database'),
        os.path.join(current_dir, 'models'),
        os.path.join(current_dir, 'utils')
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.append(path)

class FixedModernApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.create_widgets()
        self.load_data()

    def setup_window(self):
        """إعداد النافذة"""
        self.title("نظام إدارة محل صيانة الجوالات 2025")
        self.geometry("1200x800")
        self.configure(bg='#f8fafc')
        
        # توسيط النافذة
        self.update_idletasks()
        width = 1200
        height = 800
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الألوان العصرية
        self.colors = {
            'primary': '#6366f1',
            'success': '#10b981',
            'warning': '#f59e0b',
            'accent': '#06b6d4',
            'dark': '#1f2937',
            'gray': '#6b7280'
        }
        
        # الشريط العلوي
        header = tk.Frame(self, bg=self.colors['primary'], height=80)
        header.pack(fill='x')
        header.pack_propagate(False)
        
        title = tk.Label(header, text="📱 نظام إدارة محل صيانة الجوالات 2025",
                        font=('Arial', 18, 'bold'),
                        bg=self.colors['primary'], fg='white')
        title.pack(expand=True)
        
        # منطقة البطاقات
        cards_frame = tk.Frame(self, bg='#f8fafc')
        cards_frame.pack(fill='x', padx=20, pady=20)
        
        # بطاقات الإحصائيات
        self.create_card(cards_frame, "👥", "العملاء", "0", self.colors['primary'])
        self.create_card(cards_frame, "🔧", "الطلبات", "0", self.colors['warning'])
        self.create_card(cards_frame, "📦", "المخزون", "0", self.colors['success'])
        self.create_card(cards_frame, "💰", "الإيرادات", "0 ريال", self.colors['accent'])
        
        # منطقة المحتوى
        content_frame = tk.Frame(self, bg='#f8fafc')
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # القائمة الجانبية
        sidebar = tk.Frame(content_frame, bg='white', width=250)
        sidebar.pack(side='left', fill='y', padx=(0, 10))
        sidebar.pack_propagate(False)
        
        menu_title = tk.Label(sidebar, text="📋 القوائم الرئيسية",
                             font=('Arial', 14, 'bold'),
                             bg='white', fg=self.colors['dark'])
        menu_title.pack(pady=20)
        
        # أزرار القوائم
        self.create_menu_button(sidebar, "👥 إدارة العملاء", self.colors['primary'], self.open_customers)
        self.create_menu_button(sidebar, "🔧 طلبات الصيانة", self.colors['warning'], self.open_orders)
        self.create_menu_button(sidebar, "📦 إدارة المخزون", self.colors['success'], self.open_inventory)
        self.create_menu_button(sidebar, "📊 التقارير", self.colors['accent'], self.open_reports)
        
        # المنطقة الرئيسية
        main_area = tk.Frame(content_frame, bg='white')
        main_area.pack(side='right', fill='both', expand=True)
        
        welcome = tk.Label(main_area, text="🎉\n\nمرحباً بك في النظام العصري!\n\nاختر من القوائم الجانبية للبدء",
                          font=('Arial', 16),
                          bg='white', fg=self.colors['dark'],
                          justify='center')
        welcome.pack(expand=True)
        
        # شريط الحالة
        status_bar = tk.Frame(self, bg=self.colors['dark'], height=30)
        status_bar.pack(fill='x', side='bottom')
        status_bar.pack_propagate(False)
        
        status_label = tk.Label(status_bar, text="🟢 النظام يعمل بشكل طبيعي",
                               font=('Arial', 9),
                               bg=self.colors['dark'], fg=self.colors['success'])
        status_label.pack(side='left', padx=10, pady=5)

    def create_card(self, parent, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        card.pack(side='left', fill='both', expand=True, padx=5)
        
        icon_label = tk.Label(card, text=icon, font=('Arial', 24),
                             bg='white', fg=color)
        icon_label.pack(pady=10)
        
        value_label = tk.Label(card, text=value, font=('Arial', 16, 'bold'),
                              bg='white', fg=self.colors['dark'])
        value_label.pack()
        
        title_label = tk.Label(card, text=title, font=('Arial', 10),
                              bg='white', fg=self.colors['gray'])
        title_label.pack(pady=(0, 10))

    def create_menu_button(self, parent, text, color, command):
        """إنشاء زر قائمة"""
        btn = tk.Button(parent, text=text, command=command,
                       font=('Arial', 11, 'bold'),
                       bg=color, fg='white', relief='flat',
                       width=20, pady=8)
        btn.pack(pady=5, padx=15, fill='x')

    def load_data(self):
        """تحميل البيانات"""
        try:
            from database.db_manager import get_connection
            
            conn = get_connection()
            c = conn.cursor()
            
            # عدد العملاء
            c.execute("SELECT COUNT(*) FROM customers")
            customers_count = c.fetchone()[0]
            
            # عدد الطلبات
            c.execute("SELECT COUNT(*) FROM orders")
            orders_count = c.fetchone()[0]
            
            # عدد المخزون
            c.execute("SELECT COUNT(*) FROM inventory")
            inventory_count = c.fetchone()[0]
            
            # الإيرادات
            c.execute("SELECT SUM(amount) FROM payments")
            revenue = c.fetchone()[0] or 0
            
            conn.close()
            
            print(f"📊 البيانات: العملاء={customers_count}, الطلبات={orders_count}, المخزون={inventory_count}, الإيرادات={revenue}")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def open_customers(self):
        """فتح إدارة العملاء"""
        try:
            # استخدام الواجهة التقليدية للعملاء
            from ui.customer_ui import CustomerFrame
            customer_window = tk.Toplevel(self)
            customer_window.title("إدارة العملاء")
            customer_window.geometry("1000x700")
            customer_frame = CustomerFrame(customer_window)
            customer_frame.pack(fill='both', expand=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إدارة العملاء:\n{str(e)}")

    def open_orders(self):
        """فتح إدارة الطلبات"""
        try:
            from ui.order_ui import OrderFrame
            order_window = tk.Toplevel(self)
            order_window.title("طلبات الصيانة")
            order_window.geometry("1000x700")
            order_frame = OrderFrame(order_window)
            order_frame.pack(fill='both', expand=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح طلبات الصيانة:\n{str(e)}")

    def open_inventory(self):
        """فتح إدارة المخزون"""
        try:
            from ui.inventory_ui import InventoryFrame
            inventory_window = tk.Toplevel(self)
            inventory_window.title("إدارة المخزون")
            inventory_window.geometry("1000x700")
            inventory_frame = InventoryFrame(inventory_window)
            inventory_frame.pack(fill='both', expand=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إدارة المخزون:\n{str(e)}")

    def open_reports(self):
        """فتح التقارير"""
        try:
            from ui.report_ui import ReportFrame
            report_window = tk.Toplevel(self)
            report_window.title("التقارير والإحصائيات")
            report_window.geometry("1000x700")
            report_frame = ReportFrame(report_window)
            report_frame.pack(fill='both', expand=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح التقارير:\n{str(e)}")

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        from database.db_manager import get_connection
        
        conn = get_connection()
        c = conn.cursor()
        
        c.execute("SELECT COUNT(*) FROM customers")
        if c.fetchone()[0] == 0:
            # بيانات تجريبية مبسطة
            customers = [
                ("أحمد محمد", "0501234567", "<EMAIL>", "الرياض"),
                ("فاطمة سعد", "0509876543", "<EMAIL>", "جدة"),
                ("محمد عبدالله", "0551234567", "<EMAIL>", "الدمام")
            ]
            
            for customer in customers:
                c.execute("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)", customer)
            
            inventory = [
                ("شاشة iPhone", "IP-001", 10, 500.00, 2),
                ("بطارية Samsung", "SG-001", 15, 150.00, 3)
            ]
            
            for item in inventory:
                c.execute("INSERT INTO inventory (name, part_number, quantity, price, low_stock_threshold) VALUES (?, ?, ?, ?, ?)", item)
            
            orders = [
                (1, "Apple", "iPhone 14", "شاشة مكسورة", 500.00, "في الانتظار", "أحمد", "2024-01-20 10:00:00"),
                (2, "Samsung", "Galaxy S23", "بطارية ضعيفة", 150.00, "قيد الإصلاح", "محمد", "2024-01-19 14:00:00")
            ]
            
            for order in orders:
                c.execute("INSERT INTO orders (customer_id, device_brand, device_model, issue, estimated_cost, status, technician, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", order)
            
            conn.commit()
            print("✅ تم إنشاء البيانات التجريبية")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 " + "=" * 50)
    print("🚀 الواجهة العصرية المصححة 2025")
    print("🚀 " + "=" * 50)
    
    setup_paths()
    
    try:
        print("🗄️ تهيئة قاعدة البيانات...")
        from database.db_manager import init_db
        init_db()
        print("✅ تم تهيئة قاعدة البيانات")
        
        print("📊 إنشاء البيانات التجريبية...")
        create_sample_data()
        
        print("🎨 تشغيل الواجهة العصرية...")
        app = FixedModernApp()
        print("✅ تم تشغيل التطبيق بنجاح!")
        app.mainloop()
        
    except Exception as e:
        error_msg = f"❌ خطأ: {e}"
        print(error_msg)
        messagebox.showerror("خطأ", error_msg)

if __name__ == "__main__":
    main()
